/* empty css             */import{C as E,c as T,h as N}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as V,r as o,A as O,I as S,E as z,o as b,e as B,f as i,g as a,J as K,K as L,q as A,w as g,n as y,L as M,M as R,N as U,h as q,t as G,O as J}from"./index-d4ffb1a1.js";import"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{D as $}from"./DataPreviewDialog-3c7681e1.js";import{P as k}from"./type-bba1e229.js";import{_ as j}from"./_plugin-vue_export-helper-c27b6911.js";import"./construct-a2f67563.js";const F={class:"data-import"},H={class:"search-header"},Q={class:"table-container"},W={class:"operation-buttons"},X=["onClick"],Y={class:"pagination-container"},r=10,Z=V({__name:"assetPackage",setup(ee){const c=o(""),p=o(!1),m=o([]),d=o(!1),h=o(k.OPERATION),u=o(null),f=o(0),n=o(1);O(()=>{v()});async function v(){p.value=!0;const s={page:n.value,page_size:r,search:c.value,package_status:"available"},{data:e}=await S(s),{state:t,msg:_}=e;t==="success"?(m.value=e.data.results,f.value=e.data.count):z.error(_),p.value=!1}function w(){n.value=1,v()}function C(s){n.value=s,v()}function P(s){h.value=k.ORIGINAL,u.value=s.id||null,d.value=!0}function D(){d.value=!1,u.value=null}return(s,e)=>{const t=J,_=M,x=R,I=U;return b(),B("div",F,[i("div",H,[a(E,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=l=>c.value=l),placeholder:"搜索资产包名称",onKeyup:K(w,["enter"]),onClick:w},null,8,["modelValue","onKeyup"])]),i("div",Q,[L((b(),A(_,{data:m.value,border:"",style:{width:"100%"},"cell-style":y(T),"header-cell-style":y(N)},{default:g(()=>[a(t,{type:"index",label:"序号",width:"80",align:"center"},{default:g(({$index:l})=>[q(G((n.value-1)*r+l+1),1)]),_:1}),a(t,{prop:"package_name",label:"资产包名称","min-width":"200",align:"center","show-overflow-tooltip":""}),a(t,{prop:"source_file_name",label:"原文件","min-width":"180",align:"center","show-overflow-tooltip":""}),a(t,{prop:"file_size_display",label:"文件大小","min-width":"100",align:"center"}),a(t,{prop:"uploader_name",label:"上传人","min-width":"100",align:"center"}),a(t,{prop:"upload_time",label:"上传时间","min-width":"160",align:"center"}),a(t,{label:"操作",width:"150",fixed:"right",align:"center"},{default:g(({row:l})=>[i("div",W,[i("div",{onClick:ae=>P(l),class:"operation-btn edit-btn"},"预览",8,X)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[I,p.value]]),i("div",Y,[a(x,{"current-page":n.value,"onUpdate:currentPage":e[1]||(e[1]=l=>n.value=l),"page-size":r,"onUpdate:pageSize":e[2]||(e[2]=l=>r=l),total:f.value,layout:"prev, pager, next",onCurrentChange:C,background:""},null,8,["current-page","total"])])]),a($,{visible:d.value,"preview-type":h.value,"package-id":u.value,onClose:D},null,8,["visible","preview-type","package-id"])])}}});const ue=j(Z,[["__scopeId","data-v-4c03a1b4"]]);export{ue as default};

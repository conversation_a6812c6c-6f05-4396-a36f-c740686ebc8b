<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import { ElMessage } from "element-plus";
import type { FieldType, PlanField, EditPlanParams, Plan, FieldTypeOption } from "../type";
import { FieldType as FieldTypeEnum } from "../type";
import { getMediationPlanDetail, getMediationCase, getDataImportDetail, expressionCalculation } from '@/axios/system';

const props = defineProps<{
  showDialog: boolean,
  planData: Plan,  // 要编辑的方案数据
  currentMode?: 'mediationCases' | 'asset', // 当前模式
  mediationCaseOptions?: Array<{ label: string; value: string }>, // 调解案件选项
  assetPackageOptions?: Array<{ label: string; value: string }> // 资产包选项
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: EditPlanParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 方案基础信息
const planForm = ref({
  asset_package: null, // 资产包下拉框
  mediation_case: null, // 调解案件下拉框
  plan_name: '', // 方案名称
})

// 动态字段列表（编辑模式下支持新增、删除和修改）
const dynamicFields: Ref<PlanField[]> = ref([])

// 表达式变量列表
const expressionVariables = ref<Array<{code: string, name: string}>>([])

// 监听弹框显示状态，获取变量数据
watch(() => props.showDialog, (newVal) => {
  if (newVal && props.planData) {
    loadExpressionVariables()
  }
})

// 获取表达式变量数据
async function loadExpressionVariables() {
  expressionVariables.value = props.planData.mapped_field_names.map((name: string) => ({
    code: name,
    name: name
  }))
}

// 自定义表达式编辑器相关状态
const showVariableDropdown: Ref<Record<number, boolean>> = ref({})
const filteredVariables: Ref<Record<number, Array<{ code: string, name: string }>>> = ref({})
const cursorPosition: Ref<Record<number, number>> = ref({})
const variableSearchText: Ref<Record<number, string>> = ref({})

// 字段类型选项配置
const fieldTypeOptions: FieldTypeOption[] = [
  { label: '文本类型', value: FieldTypeEnum.TEXTAREA, icon: 'jt-24-edit' },
  { label: '日期类型', value: FieldTypeEnum.DATE, icon: 'jt-24-calendar' },
  { label: '金额类型', value: FieldTypeEnum.AMOUNT, icon: 'jt-24-money' },
]

// 计算当前模式，默认为调解案件模式
const currentMode = computed(() => props.currentMode || 'mediationCases')

// 计算对话框标题
const dialogTitle = computed(() => {
  return currentMode.value === 'asset' ? '编辑资产包方案' : '编辑调解案件方案'
})

// 表单验证规则
const rules = computed(() => {
  return {
    plan_name : [
      { required: true, message: '请输入方案名称', trigger: 'blur' },
    ]
  }
})

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 监听方案数据变化
watch(() => props.planData, loadPlanData, { deep: true })

// 弹框打开时加载数据
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    loadPlanData(props.planData)
  }
}

// 加载方案数据
function loadPlanData(planData: Plan) {
  if (!planData) return
  if (currentMode.value === 'asset') {
    // 资产包模式：设置资产包ID
    planForm.value.asset_package = planData.asset_package
  } else {
    // 调解案件模式：设置调解案件ID
    planForm.value.mediation_case = planData.mediation_case
  }
  
  // 设置方案名称
  planForm.value.plan_name = planData.plan_name

  // 深拷贝字段数据，已存在的字段默认设为必填
  dynamicFields.value = planData.plan_config ? JSON.parse(JSON.stringify(planData.plan_config)).map((field: PlanField) => {
    // 确保有默认逻辑类型
    if (!field.logic_type) {
      field.logic_type = 'result_calculation'
    }
    // 确保有表达式和预览字段
    if (!field.expression) {
      field.expression = ''
    }
    if (!field.preview) {
      field.preview = ''
    }
    // 修复InvalidCharacterError: 确保字段ID为有效字符串
    if (!field.id || typeof field.id !== 'string' || field.id.trim() === '') {
      field.id = generateFieldId()
    }
    return field
  }) : []
}

// 关闭弹框
function close() {
  emit('close')
}

// 生成唯一字段ID - 修复InvalidCharacterError: 确保ID始终为有效字符串
function generateFieldId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 11)
  return `field_${timestamp}_${random}`
}

// 添加新字段
function addField() {
  const newField: PlanField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    logic_type: 'result_calculation', // 默认逻辑处理类型：结果运算
    expression: '', // 表达式
    preview: '' // 预览内容
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 处理逻辑处理类型变化
function handleLogicTypeChange(value: any, fieldIndex: number) {
  const logicType = String(value)
  
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].logic_type = logicType
    // 如果当前字段有表达式内容，重新解析预览
    const currentExpression = dynamicFields.value[fieldIndex].expression
    if (currentExpression && currentExpression.trim()) {
      // 直接调用parseExpressionContent，因为currentExpression已经是存储格式（{变量名}）
      parseExpressionContent(currentExpression, fieldIndex)
    }
  }
}

// 表达式编辑相关函数
function handleExpressionInput(displayValue: string, fieldIndex: number) {
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].expression = displayValue
  }
}

// 解析表达式内容
async function parseExpressionContent(expression: string, fieldIndex: number) {
  // 获取当前字段的逻辑处理类型
  const currentField = dynamicFields.value[fieldIndex]
  const selectedLogicType = currentField?.logic_type || 'result_calculation'
  
  // 如果是资产包tab参数asset_package_id= props.planData.asset_package; 如果是调解案件tab参数：mediation_case_id = props.planData.mediation_case;
  const params = {
    asset_package_id: props.planData?.asset_package ? Number(props.planData.asset_package) : undefined,
    mediation_case_id: props.planData?.mediation_case ? Number(props.planData.mediation_case) : undefined,
    expression: expression, // 经过processChineseVariables处理的大括号格式表达式
    logic_type: selectedLogicType // 用户选中的逻辑处理类型值
  }
  const {data} = await expressionCalculation(params)
  
  // 将接口返回的response.data.data值赋值给内容预览preview
  const previewResult = data.data
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].preview = String(previewResult)
  }
}

async function handleExpressionBlurEvent(event: Event, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement
  const displayValue = target.value
  
  if (!dynamicFields.value[fieldIndex]) return
  
  const field = dynamicFields.value[fieldIndex]
  field.expression = displayValue
  
  // 隐藏变量下拉框
  hideVariableDropdown(fieldIndex)
  
  // 如果表达式为空，清空预览
  if (!displayValue.trim()) {
    field.preview = ''
    return
  }
  
  // 调用表达式计算接口
  const logicType = field.logic_type || 'result_calculation'
  // 如果是资产包tab参数asset_package_id= props.planData.asset_package; 如果是调解案件tab参数：mediation_case_id = props.planData.mediation_case;
  const params = {
    asset_package_id: props.planData?.asset_package ? Number(props.planData.asset_package) : undefined,
    mediation_case_id: props.planData?.mediation_case ? Number(props.planData.mediation_case) : undefined,
    expression: displayValue,
    logic_type: logicType
  }
  const response = await expressionCalculation(params)
  
  if (response.data.state === 'success') {
    field.preview = String(response.data.data)
  } else {
    field.preview = response.data.msg
  }
}

function handleExpressionKeydown(event: KeyboardEvent, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement
  cursorPosition.value[fieldIndex] = target.selectionStart || 0

  if (event.key === '@') {
    // 记录@符号输入时的精确光标位置
    setTimeout(() => {
      cursorPosition.value[fieldIndex] = target.selectionStart || 0
    }, 0)
    showVariableDropdown.value[fieldIndex] = true
    filteredVariables.value[fieldIndex] = expressionVariables.value
    variableSearchText.value[fieldIndex] = ''
  } else if (event.key === 'Escape') {
    hideVariableDropdown(fieldIndex)
  }
}
function searchVariables(searchText: string, fieldIndex: number) {
  if (!searchText || searchText.trim() === '') {
    filteredVariables.value[fieldIndex] = expressionVariables.value
  } else {
    const filtered = expressionVariables.value.filter(variable =>
      variable.name.toLowerCase().includes(searchText.toLowerCase()) ||
      variable.code.toLowerCase().includes(searchText.toLowerCase())
    )
    filteredVariables.value[fieldIndex] = filtered
  }
}

function hideVariableDropdown(fieldIndex: number) {
  showVariableDropdown.value[fieldIndex] = false
  filteredVariables.value[fieldIndex] = []
}

function insertVariableAtCursor(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return

  const field = dynamicFields.value[fieldIndex]
  const currentExpression = field.expression || ''
  const cursorPos = cursorPosition.value[fieldIndex] || currentExpression.length

  const beforeCursor = currentExpression.substring(0, cursorPos)
  const afterCursor = currentExpression.substring(cursorPos)

  // 改进的@符号检测和移除逻辑
  let finalBefore = beforeCursor

  // 检查光标前是否有@符号（可能不在最末尾）
  const atIndex = beforeCursor.lastIndexOf('@')
  if (atIndex !== -1) {
    // 检查@符号后是否只有空白字符或者没有其他字符
    const afterAt = beforeCursor.substring(atIndex + 1)
    if (afterAt.trim() === '' || /^[\s]*$/.test(afterAt)) {
      // 移除@符号及其后的空白字符
      finalBefore = beforeCursor.substring(0, atIndex)
    }
  }

  const variableName = `{${variable.name}}`
  const newExpression = finalBefore + variableName + afterCursor
  field.expression = newExpression

  hideVariableDropdown(fieldIndex)
}

function getExpressionPlaceholder(expression: string) {
  if (!expression || expression.trim() === '') {
    return '请输入表达式，支持中文、数字、运算符（+ - * /），输入@可选择变量'
  }
  return ''
}

function formatExpressionForDisplay(expression: string): string {
  if (!expression) return ''
  return expression.replace(/\{([^}]+)\}/g, '<span class="variable-highlight">{$1}</span>')
}
/* 
// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const planTitle = planForm.value.plan_name

  const renderData = {
    planTitle,
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type,
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: PlanField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    default:
      return String(field.value || '')
  }
} */

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    if (!field.title.trim()) {
      ElMessage.error(`第${i + 1}个字段的标题不能为空`)
      return false
    }
    
    /* if (field.required) {
      if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    } */
  }
  return true
}

// 确认编辑方案
async function ensureEdit() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 检查是否至少有一个字段
    if (dynamicFields.value.length === 0) {
      ElMessage.error('请至少保留一个字段')
      return
    }
    
    // 构造提交数据
    const params: EditPlanParams = {
      id: props.planData.id!,
      plan_name: planForm.value.plan_name,
      plan_config: dynamicFields.value,
      // jsonData: generateWechatRenderData(),
      // 根据模式添加可选参数
      ...(currentMode.value === 'asset' && {
        asset_package: planForm.value.asset_package
      }),
      ...(currentMode.value === 'mediationCases' && {
        mediation_case: planForm.value.mediation_case
      })
    }
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" :title="dialogTitle">
    <div class="edit-plan-content">
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="110px">
        <!-- 资产包模式：资产包下拉框 -->
        <el-form-item v-if="currentMode === 'asset'" label="资产包" prop="asset_package">
          <el-select
            v-model="planForm.asset_package"
            filterable
            disabled
            style="width: 100%">
            <el-option
              v-for="option in assetPackageOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 调解案件模式：调解案件下拉框 -->
        <el-form-item v-else label="调解案件" prop="mediation_case">
          <el-select
            v-model="planForm.mediation_case"
            filterable
            disabled
            style="width: 100%">
            <el-option
              v-for="option in mediationCaseOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 方案名称输入框 -->
        <el-form-item label="方案名称" prop="plan_name">
          <el-input
            v-model="planForm.plan_name"
            placeholder="例如方案一、方案二"
            maxlength="50"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div class="dynamic-fields-section">
        <div class="section-header">
          <h3>字段配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id || `field-${index}`"
            class="field-item">
            
            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="28" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段配置 -->
            <div class="field-config">
              <!-- 字段类型 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>

              <!-- 字段标题 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>

            <!-- 逻辑处理类型选择区域 -->
            <div class="logic-type-section" :key="`logic-type-${field.id}-${index}`">
              <label class="config-label">逻辑类型：</label>
              <el-radio-group
                v-model="field.logic_type"
                class="logic-type-radio-group"
                @change="(value) => handleLogicTypeChange(value, index)"
                :key="`radio-group-${field.id}-${index}`"
              >
                <el-radio
                  label="text_format"
                  size="small"
                  :key="`text-format-${field.id}-${index}`"
                >
                  文本格式化
                </el-radio>
                <el-radio
                  label="result_calculation"
                  size="small"
                  :key="`result-calculation-${field.id}-${index}`"
                >
                  结果运算
                </el-radio>
              </el-radio-group>
            </div>

            <!-- 表达式编辑区域 -->
            <div class="expression-section">
              <label class="config-label">表达式编辑：</label>

              <!-- 自定义表达式编辑器 -->
              <div class="custom-expression-editor">
                <div class="expression-input-container">
                  <el-input
                    v-model="field.expression"
                    @input="handleExpressionInput($event, index)"
                    type="textarea"
                    :rows="3"
                    :placeholder="getExpressionPlaceholder(field.expression)"
                    @blur="handleExpressionBlurEvent($event, index)"
                    @keydown="handleExpressionKeydown($event, index)"
                    class="expression-textarea"
                  />

                  <!-- 变量选择下拉框 -->
                  <div
                    v-if="showVariableDropdown[index]"
                    class="variable-dropdown"
                  >
                    <div class="dropdown-header">
                      <span>选择变量：</span>
                      <el-button size="small" text @click="hideVariableDropdown(index)">×</el-button>
                    </div>

                    <!-- 变量搜索框 -->
                    <div class="variable-search">
                      <el-input
                        v-model="variableSearchText[index]"
                        size="small"
                        placeholder="搜索变量名称..."
                        @input="searchVariables(variableSearchText[index] || '', index)"
                        clearable
                      />
                    </div>

                    <div class="variable-list">
                      <div
                        v-for="variable in filteredVariables[index] || expressionVariables"
                        :key="variable.code"
                        @click.stop="insertVariableAtCursor(variable, index)"
                        @mousedown.prevent
                        class="variable-item"
                      >
                        <span class="variable-name">{{ variable.name }}</span>
                      </div>

                      <!-- 无搜索结果提示 -->
                      <div
                        v-if="(filteredVariables[index] || expressionVariables).length === 0"
                        class="no-results"
                      >
                        未找到匹配的变量
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表达式预览区域 -->
            <div v-if="field.expression && field.expression.trim()" class="expression-preview">
              <span class="preview-label">表达式预览：</span>
              <span class="preview-content" v-html="formatExpressionForDisplay(field.expression)"></span>
            </div>

            <!-- 内容预览区域 -->
            <div class="preview-section">
              内容预览：<div class="preview-content">{{ field.preview }}</div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置方案内容</p>
        </div>
      </div>
      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 15px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 5px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .expression-section {
        margin-bottom: 22px;
        display: flex;

        .config-label {
          display: block;
          margin-bottom: 8px;
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  .expression-preview {
    margin-top: 16px;
    margin-bottom: 18px;
    line-height: 1.4;
    font-size: 16px;
    color: #666;
    font-weight: 500;
    .config-label{
      display: inline-block;
      width: 96px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }
    .preview-content {
      font-size: 16px;
      color: #666;
      font-weight: 500;

      :deep(.variable-highlight) {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: 500;
        border: 1px solid #bbdefb;
      }
    }
  }
  
  .preview-section {
    display: flex;
    font-size: 16px;
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
    gap: 16px;
  }

  // 逻辑处理类型选择区域样式
  .logic-type-section {
    margin-bottom: 16px;

    .config-label {
      display: inline-block;
      width: 96px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }

    .logic-type-radio-group {
      .el-radio {
        margin-right: 16px;

        :deep(.el-radio__label) {
          font-size: 16px;
        }

        &.is-checked .el-radio__label {
          color: #409eff;
        }
      }
    }
  }

  // 自定义表达式编辑器样式
  .custom-expression-editor {
    position: relative;
    width: 89%;

    .expression-input-container {
      position: relative;

      .expression-textarea {
        :deep(.el-textarea__inner) {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          line-height: 1.5;
          resize: vertical;
          min-height: 80px;
        }
      }

      .variable-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 2000;
        max-height: 300px;
        overflow-y: auto;
        pointer-events: auto;

        .dropdown-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #ebeef5;
          background: #f5f7fa;
          font-size: 12px;
          color: #606266;
          font-weight: 500;
        }

        .variable-search {
          padding: 8px 12px;
          border-bottom: 1px solid #ebeef5;
          background: #fafafa;
        }

        .variable-list {
          .variable-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
            user-select: none;
            pointer-events: auto;

            &:hover {
              background: #f5f7fa;
              color: #409eff;
            }

            &:active {
              background: #e6f7ff;
            }

            &:last-child {
              border-bottom: none;
            }

            .variable-name {
              font-size: 13px;
              font-weight: 500;
            }
          }

          .no-results {
            padding: 16px 12px;
            text-align: center;
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .edit-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}

:deep(.el-input__inner),:deep(.el-textarea__inner) {
  font-size: 16px;
}
</style>

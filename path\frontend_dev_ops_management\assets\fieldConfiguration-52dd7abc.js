/* empty css             */import{C as _e,c as fe,h as pe}from"./headerCellStyle-3128036a.js";import{C as B}from"./CustomButton-777ba9d4.js";/* empty css                     *//* empty css                 */import{_ as Y}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{d as X,r as p,v as Z,o as v,q as T,w as o,K as M,e as F,g as l,F as z,B as q,G as ee,f as h,h as I,E as i,j as le,k as ae,X as te,U as se,aF as oe,aG as ie,l as ne,N as G,p as H,m as J,aH as de,b as ve,A as me,aI as he,n as W,ak as be,aJ as ge,aK as ke,aL as ye,L as we,t as xe,aM as Ce,O as Ve}from"./index-d4ffb1a1.js";import{F as L,V as R}from"./type-bba1e229.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";import{D as Ee}from"./DeleteConfirmDialog-5eb37e5a.js";/* empty css                                                                            */const ue=_=>(H("data-v-b8eed6ed"),_=_(),J(),_),De={class:"dialog-content"},$e={key:0,class:"desensitize-config"},Ue={class:"footer-actions"},Fe=ue(()=>h("i",{class:"jt-20-ensure"},null,-1)),Se=ue(()=>h("i",{class:"jt-20-delete"},null,-1)),Te=X({__name:"fieldConfigurationAdd",props:{visible:{type:Boolean}},emits:["close","success"],setup(_,{emit:V}){const w=_,c=p(!1),e=p({field_name:"",field_type:L.TEXT,data_validation:R.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}),x=p([]),g=p([]);async function E(){try{const{data:n}=await de(),{state:t,msg:$}=n;if(t==="success"){const u=Object.entries(n.data.field_types).map(([m,b])=>({label:b,value:m})),C=Object.entries(n.data.validation_types).map(([m,b])=>({label:b,value:m}));x.value=u,g.value=C}else i.error($)}catch{i.error("获取字段类型失败")}}Z(()=>w.visible,n=>{n&&(D(),E())});function D(){e.value={field_name:"",field_type:L.TEXT,data_validation:R.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}}async function O(){if(!e.value.field_name.trim()){i.error("字段名称不能为空");return}if(e.value.is_masked){if(e.value.prefix_keep_chars===0||e.value.prefix_keep_chars===null){i.error("前保留字符数不能为0");return}if(e.value.suffix_keep_chars===0||e.value.suffix_keep_chars===null){i.error("后保留字符数不能为0");return}}else e.value.is_masked||(e.value.prefix_keep_chars=0,e.value.suffix_keep_chars=0);V("success",e.value)}function k(){V("close")}return(n,t)=>{const $=le,u=ae,C=te,m=se,b=oe,S=ie,N=ne,j=G;return v(),T(Y,{visible:n.visible,"onUpdate:visible":k,width:"800px",title:"新增字段配置"},{default:o(()=>[M((v(),F("div",De,[l(N,{model:e.value,"label-width":"140px","label-position":"left",class:"field-config-form"},{default:o(()=>[l(u,{label:"字段名称",required:""},{default:o(()=>[l($,{modelValue:e.value.field_name,"onUpdate:modelValue":t[0]||(t[0]=a=>e.value.field_name=a),placeholder:"请输入字段名称",maxlength:"50","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(u,{label:"字段类型",required:""},{default:o(()=>[l(m,{modelValue:e.value.field_type,"onUpdate:modelValue":t[1]||(t[1]=a=>e.value.field_type=a),placeholder:"选择类型",style:{width:"100%"}},{default:o(()=>[(v(!0),F(z,null,q(x.value,a=>(v(),T(C,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"数据校验",required:""},{default:o(()=>[l(m,{modelValue:e.value.data_validation,"onUpdate:modelValue":t[2]||(t[2]=a=>e.value.data_validation=a),placeholder:"选择校验",style:{width:"100%"}},{default:o(()=>[(v(!0),F(z,null,q(g.value,a=>(v(),T(C,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"是否脱敏"},{default:o(()=>[l(b,{modelValue:e.value.is_masked,"onUpdate:modelValue":t[3]||(t[3]=a=>e.value.is_masked=a)},null,8,["modelValue"])]),_:1}),e.value.is_masked?(v(),F("div",$e,[l(u,{label:"前保留字符数"},{default:o(()=>[l(S,{modelValue:e.value.prefix_keep_chars,"onUpdate:modelValue":t[4]||(t[4]=a=>e.value.prefix_keep_chars=a),min:0,max:99,placeholder:"前保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(u,{label:"后保留字符数"},{default:o(()=>[l(S,{modelValue:e.value.suffix_keep_chars,"onUpdate:modelValue":t[5]||(t[5]=a=>e.value.suffix_keep_chars=a),min:0,max:99,placeholder:"后保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):ee("",!0)]),_:1},8,["model"]),h("div",Ue,[l(B,{onClick:O,height:34,loading:c.value,"btn-type":"blue"},{default:o(()=>[Fe,I("确认 ")]),_:1},8,["loading"]),l(B,{onClick:k,height:34},{default:o(()=>[Se,I("取消 ")]),_:1})])])),[[j,c.value]])]),_:1},8,["visible"])}}});const Oe=P(Te,[["__scopeId","data-v-b8eed6ed"]]),re=_=>(H("data-v-9f32a976"),_=_(),J(),_),Ie={class:"dialog-content"},Ne={key:0,class:"desensitize-config"},je={class:"footer-actions"},Be=re(()=>h("i",{class:"jt-20-ensure"},null,-1)),Ae=re(()=>h("i",{class:"jt-20-delete"},null,-1)),ze=X({__name:"fieldConfigurationEdit",props:{visible:{type:Boolean},editData:{}},emits:["close","success"],setup(_,{emit:V}){const w=_,c=p(!1),e=p({field_name:"",field_type:L.TEXT,data_validation:R.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}),x=p([]),g=p([]);async function E(){try{const{data:n}=await de(),{state:t,msg:$}=n;if(t==="success"){const u=Object.entries(n.data.field_types).map(([m,b])=>({label:b,value:m})),C=Object.entries(n.data.validation_types).map(([m,b])=>({label:b,value:m}));x.value=u,g.value=C}else i.error($)}catch{i.error("获取字段类型失败")}}Z([()=>w.visible,()=>w.editData],([n,t])=>{n&&t&&(D(t),E())});function D(n){e.value={...n}}async function O(){if(!e.value.field_name.trim()){i.error("字段名称不能为空");return}if(e.value.is_masked){if(e.value.prefix_keep_chars===0||e.value.prefix_keep_chars===null){i.error("前保留字符数不能为0");return}if(e.value.suffix_keep_chars===0||e.value.suffix_keep_chars===null){i.error("后保留字符数不能为0");return}}else e.value.is_masked||(e.value.prefix_keep_chars=0,e.value.suffix_keep_chars=0);V("success",e.value)}function k(){V("close")}return(n,t)=>{const $=le,u=ae,C=te,m=se,b=oe,S=ie,N=ne,j=G;return v(),T(Y,{visible:n.visible,"onUpdate:visible":k,width:"800px",title:"编辑字段配置"},{default:o(()=>[M((v(),F("div",Ie,[l(N,{model:e.value,"label-width":"140px","label-position":"left",class:"field-config-form"},{default:o(()=>[l(u,{label:"字段名称",required:""},{default:o(()=>[l($,{modelValue:e.value.field_name,"onUpdate:modelValue":t[0]||(t[0]=a=>e.value.field_name=a),placeholder:"请输入字段名称",maxlength:"50","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(u,{label:"字段类型"},{default:o(()=>[l(m,{modelValue:e.value.field_type,"onUpdate:modelValue":t[1]||(t[1]=a=>e.value.field_type=a),placeholder:"选择类型",style:{width:"100%"}},{default:o(()=>[(v(!0),F(z,null,q(x.value,a=>(v(),T(C,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"数据校验"},{default:o(()=>[l(m,{modelValue:e.value.data_validation,"onUpdate:modelValue":t[2]||(t[2]=a=>e.value.data_validation=a),placeholder:"选择校验",style:{width:"100%"}},{default:o(()=>[(v(!0),F(z,null,q(g.value,a=>(v(),T(C,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"是否脱敏"},{default:o(()=>[l(b,{modelValue:e.value.is_masked,"onUpdate:modelValue":t[3]||(t[3]=a=>e.value.is_masked=a)},null,8,["modelValue"])]),_:1}),e.value.is_masked?(v(),F("div",Ne,[l(u,{label:"前保留字符数"},{default:o(()=>[l(S,{modelValue:e.value.prefix_keep_chars,"onUpdate:modelValue":t[4]||(t[4]=a=>e.value.prefix_keep_chars=a),min:0,max:99,placeholder:"前保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(u,{label:"后保留字符数"},{default:o(()=>[l(S,{modelValue:e.value.suffix_keep_chars,"onUpdate:modelValue":t[5]||(t[5]=a=>e.value.suffix_keep_chars=a),min:0,max:99,placeholder:"后保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):ee("",!0)]),_:1},8,["model"]),h("div",je,[l(B,{onClick:O,height:34,loading:c.value,"btn-type":"blue"},{default:o(()=>[Be,I("确认 ")]),_:1},8,["loading"]),l(B,{onClick:k,height:34},{default:o(()=>[Ae,I("取消 ")]),_:1})])])),[[j,c.value]])]),_:1},8,["visible"])}}});const qe=P(ze,[["__scopeId","data-v-9f32a976"]]),Ke=_=>(H("data-v-2c69332a"),_=_(),J(),_),Le={class:"field-configuration"},Re={class:"search-header"},Xe=Ke(()=>h("i",{class:"jt-20-add"},null,-1)),Me={class:"table-container"},Ge={class:"operation-buttons"},He=["onClick"],Je=["onClick"],Pe=["onClick"],Qe=["onClick"],We=X({__name:"fieldConfiguration",setup(_){const V=p(""),w=p(!1),c=p([]),e=p(!1),x=p(!1),g=p(null),E=p(!1),D=p(null),O=ve({page:1,page_size:9999,total:0});me(()=>{k()});async function k(){w.value=!0;const s={page:O.page,page_size:O.page_size,search:V.value},{data:d}=await he(s),{state:r,msg:y}=d;r==="success"?(c.value=d.data.results,w.value=!1):(i.error(y),w.value=!1)}function n(){be("搜索","数据治理-字段配置"),k()}async function t(s){if(s===0){i.warning("已经是第一行");return}const d=c.value[s];c.value[s]=c.value[s-1],c.value[s-1]=d,await u()}async function $(s){if(s===c.value.length-1){i.warning("已经是最后一行");return}const d=c.value[s];c.value[s]=c.value[s+1],c.value[s+1]=d,await u()}async function u(){try{w.value=!0;const s=c.value.map((f,A)=>({id:f.id,field_name:f.field_name,field_type:f.field_type,data_validation:f.data_validation||f.validation,is_masked:f.is_masked!==void 0?f.is_masked:f.isDesensitize,prefix_keep_chars:f.prefix_keep_chars!==void 0?f.prefix_keep_chars:f.prefixKeepCount,suffix_keep_chars:f.suffix_keep_chars!==void 0?f.suffix_keep_chars:f.suffixKeepCount,display_order:A+1})),{data:d}=await Ce({items:s}),{state:r,msg:y}=d;r==="success"?(i.success(y),await k()):i.error(y||"保存失败")}catch(s){i.error(s||"保存失败")}finally{w.value=!1}}function C(){e.value=!0}function m(s){g.value={...s},x.value=!0}function b(){e.value=!1}function S(){x.value=!1,g.value=null}async function N(s){const{data:d}=await ge(s),{state:r,msg:y}=d;r==="success"?(i.success(y),e.value=!1,k()):(i.error(y),e.value=!1)}async function j(s){const{data:d}=await ke(s,g.value.id),{state:r,msg:y}=d;r==="success"?(i.success(y),x.value=!1,g.value=null,k()):(i.error(y),x.value=!1,g.value=null)}function a(s){D.value=s,E.value=!0}async function ce(){if(D.value)try{const{data:s}=await ye(D.value.id),{state:d,msg:r}=s;d==="success"?(i.success(r),k()):i.error(r)}catch{i.error("删除失败，请重试")}finally{E.value=!1,D.value=null}}return(s,d)=>{var A;const r=Ve,y=we,f=G;return v(),F("div",Le,[h("div",Re,[l(_e,{modelValue:V.value,"onUpdate:modelValue":d[0]||(d[0]=U=>V.value=U),placeholder:"搜索字段名称、类型",onClick:n},null,8,["modelValue"]),l(B,{onClick:C,height:34},{default:o(()=>[Xe,I("新增字段")]),_:1})]),h("div",Me,[M((v(),T(y,{data:c.value,border:"",style:{width:"100%"},"cell-style":W(fe),"header-cell-style":W(pe),"max-height":730},{default:o(()=>[l(r,{type:"index",label:"序号",width:"80",align:"center"}),l(r,{prop:"field_name",label:"字段名称","min-width":"210",align:"center"}),l(r,{prop:"field_type_cn",label:"字段类型","min-width":"100",align:"center"}),l(r,{prop:"data_validation_cn",label:"数据校验","min-width":"100",align:"center"}),l(r,{label:"是否脱敏","min-width":"60",align:"center"},{default:o(({row:U})=>[I(xe(U.is_masked?"是":"否"),1)]),_:1}),l(r,{prop:"prefix_keep_chars",label:"前保留字符数","min-width":"70",align:"center"}),l(r,{prop:"suffix_keep_chars",label:"后保留字符数","min-width":"70",align:"center"}),l(r,{label:"操作",width:"310",fixed:"right",align:"center"},{default:o(({row:U,$index:Q})=>[h("div",Ge,[h("div",{onClick:K=>m(U),class:"operation-btn edit-btn"},"编辑",8,He),h("div",{onClick:K=>t(Q),class:"operation-btn move-up-btn"},"上移",8,Je),h("div",{onClick:K=>$(Q),class:"operation-btn move-down-btn"},"下移",8,Pe),h("div",{onClick:K=>a(U),class:"operation-btn delete-btn"},"删除",8,Qe)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[f,w.value]])]),l(Oe,{visible:e.value,onClose:b,onSuccess:N},null,8,["visible"]),l(qe,{visible:x.value,"edit-data":g.value,onClose:S,onSuccess:j},null,8,["visible","edit-data"]),l(Ee,{visible:E.value,title:"删除字段配置",message:`确定要删除字段「${(A=D.value)==null?void 0:A.field_name}」吗？此操作不可撤销。`,"confirm-text":"确定","cancel-text":"取消","onUpdate:visible":d[1]||(d[1]=U=>E.value=U),onConfirm:ce,onCancel:d[2]||(d[2]=U=>E.value=!1)},null,8,["visible","message"])])}}});const ul=P(We,[["__scopeId","data-v-2c69332a"]]);export{ul as default};

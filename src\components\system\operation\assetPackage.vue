<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import DataPreviewDialog from '@/components/system/dialogs/DataPreviewDialog.vue'
import type { 
  DataImportRecord, 
  PreviewDataType,
} from '../type'
import { PreviewDataType as PreviewTypeEnum } from '../type'
import { getDataImportList } from '@/axios/system'

// 搜索关键字
const search = ref('')
// 响应式数据
const loading = ref(false)
const tableData = ref<DataImportRecord[]>([])

// 预览弹框控制
const showPreviewDialog = ref(false)
const previewType = ref<PreviewDataType>(PreviewTypeEnum.OPERATION)
const previewRecordId = ref<number | null>(null)

// 分页数据
const total = ref(0)
const page = ref(1)
const page_size = 10


// 页面加载时获取数据
onMounted(() => {
  loadTableData()
})

/**
 * 加载表格数据
 */
async function loadTableData() {
  loading.value = true
  const params = {
    page: page.value,
    page_size,
    search: search.value,
    package_status:'available'
  }
  const { data } = await getDataImportList(params)
  const { state, msg } = data
  if(state === 'success') {
    tableData.value = data.data.results
    total.value = data.data.count
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

/**
 * 搜索数据
 */
function handleSearch() {
  page.value = 1
  loadTableData()
}

/**
 * 分页变化处理
 */
function handlePageChange(newPage: number) {
  page.value = newPage
  loadTableData()
}


/**
 * 预览原始数据
 * @param row 当前行数据
 */
function previewOriginalData(row: DataImportRecord) {
  previewType.value = PreviewTypeEnum.ORIGINAL
  previewRecordId.value = row.id || null
  showPreviewDialog.value = true
}

/**
 * 关闭预览弹框
 */
function handlePreviewClose() {
  showPreviewDialog.value = false
  previewRecordId.value = null
}
</script>

<template>
  <div class="data-import">
    <div class="search-header">
      <CustomInput 
        v-model="search" 
        placeholder="搜索资产包名称" 
        @keyup.enter="handleSearch"
        @click="handleSearch" />
    </div>
    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column type="index" label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ (page - 1) * page_size + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="package_name" label="资产包名称" min-width="200" align="center" show-overflow-tooltip />
        <el-table-column prop="source_file_name" label="原文件" min-width="180" align="center" show-overflow-tooltip />
        <el-table-column prop="file_size_display" label="文件大小" min-width="100" align="center"></el-table-column>
        <el-table-column prop="uploader_name" label="上传人" min-width="100" align="center" />
        <el-table-column prop="upload_time" label="上传时间" min-width="160" align="center"></el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <!-- 运营数据预览 -->
              <div @click="previewOriginalData(row)" class="operation-btn edit-btn">预览</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="page_size"
          :total="total"
          layout="prev, pager, next"
          @current-change="handlePageChange"
          background />
      </div>
    </div>

    <!-- 数据预览弹框组件 -->
    <DataPreviewDialog
      :visible="showPreviewDialog"
      :preview-type="previewType"
      :package-id="previewRecordId"
      @close="handlePreviewClose" />
  </div>
</template>

<style lang="scss" scoped>
.data-import {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  // 搜索区域样式
  .search-header {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
  }

  // 表格容器样式
  .table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table) {
      border-radius: 8px;
      
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #f5f7fa;
            color: #606266;
            font-weight: 600;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }

    // 分页样式
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }

  // Element-Plus 组件样式覆盖
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 4px;
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .data-import {
    .search-header {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
}

@media (max-width: 768px) {
  .data-import {
    padding: 10px;

    .table-container {
      .pagination-container {
        :deep(.el-pagination) {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>
import{d as c,v as _,o as u,e as r,f as s,t as p,J as v,D as l,aV as y,G as m}from"./index-d4ffb1a1.js";const f={class:"dialog_title"},k=["onKeyup"],g=s("i",{class:"jt-48-close",tabindex:"0"},null,-1),w=[g],h={class:"dialog_container"},B=c({__name:"CustomDialog",props:{visible:{type:Boolean,default:!1},title:{default:"标题"},width:{default:"380px"},markclose:{type:Boolean,default:!0}},emits:["update:visible"],setup(n,{emit:d}){const t=n;_(()=>t.visible,e=>{e&&window.addEventListener("keyup",i)});function o(){d("update:visible")}function i(e){(e.key==="Escape"||e.keyCode===27)&&(o(),window.removeEventListener("keyup",i))}return(e,a)=>t.visible?(u(),r("div",{key:0,class:"pack_dialog",onClick:a[0]||(a[0]=b=>t.markclose?o:null),tabindex:"-1"},[s("div",{class:"dialog_wrap",style:y({width:t.width})},[s("div",f,p(t.title),1),s("div",{class:"dialog_close",onClick:o,onKeyup:v(o,["esc"])},w,40,k),s("div",h,[l(e.$slots,"default")]),l(e.$slots,"footer")],4)])):m("",!0)}});export{B as _};

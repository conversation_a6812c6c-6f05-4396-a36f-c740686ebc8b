/* empty css             */import{C as E,c as k,h as V}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as R,r as l,A as B,o as v,e as L,f as o,g as a,K as N,q as S,w as r,n as f,aE as z,E as M,L as T,M as D,N as I,h as P,t as u,C as b,O as q}from"./index-d4ffb1a1.js";import{_ as A}from"./_plugin-vue_export-helper-c27b6911.js";const K={class:"sms-record"},O={class:"search-header"},U={class:"pagination-wrapper"},m=10,j=R({__name:"messageRecord",setup(F){const i=l(!1),g=l(0),n=l(1),_=l(""),h=l([]);function y(){n.value=1,p()}async function p(){i.value=!0;const c={search:_.value,page:n.value,page_size:m},{data:s}=await z(c),{state:t,msg:d}=s;t=="success"?(h.value=s.data.results,g.value=s.data.count):M.error(d),i.value=!1}function w(c){n.value=c,p()}return B(()=>{p()}),(c,s)=>{const t=q,d=T,x=D,C=I;return v(),L("div",K,[o("div",O,[a(E,{modelValue:_.value,"onUpdate:modelValue":s[0]||(s[0]=e=>_.value=e),placeholder:"搜索接收手机号、短信内容、发送失败原因",onClick:y},null,8,["modelValue"])]),o("div",null,[N((v(),S(d,{data:h.value,border:"","cell-style":f(k),"header-cell-style":f(V),class:"record-table"},{default:r(()=>[a(t,{type:"index",label:"序号",width:"60",align:"center"},{default:r(({$index:e})=>[P(u(m*(n.value-1)+e+1),1)]),_:1}),a(t,{prop:"send_time",label:"发送时间",align:"center",width:"190"}),a(t,{prop:"recipient_phone",label:"接收手机号",align:"center",width:"130"}),a(t,{prop:"content_preview",label:"短信内容预览",align:"center","min-width":"200","show-overflow-tooltip":""}),a(t,{prop:"sms_status_cn",label:"发送状态",align:"center",width:"90"},{default:r(({row:e})=>[o("span",{class:b({"text-success":e.sms_status_cn==="发送成功","text-danger":e.sms_status_cn==="待发送"||e.sms_status_cn==="发送中","text-warning":e.sms_status_cn=="发送失败"})},u(e.sms_status_cn),3)]),_:1}),a(t,{prop:"sms_type_cn",label:"短信类型",align:"center",width:"110"},{default:r(({row:e})=>[o("span",{class:b({"text-success":e.sms_type_cn==="验证码短信"||e.sms_type_cn==="通知短信","text-danger":e.sms_type_cn==="提醒短信"||e.sms_type_cn==="其他","text-warning":e.sms_type_cn=="催收短信"})},u(e.sms_type_cn),3)]),_:1}),a(t,{prop:"creditor_name",label:"债权人名称",align:"center","min-width":"150"}),a(t,{prop:"debtor_name",label:"债务人名称",align:"center",width:"150"}),a(t,{prop:"content_length",label:"内容长度",align:"center","min-width":"90"}),a(t,{prop:"delivery_time",label:"送达时间",align:"center",width:"190"}),a(t,{prop:"task_batch_id",label:"任务批次号",align:"center","min-width":"130"}),a(t,{prop:"failure_reason",label:"失败原因",align:"center",width:"150","show-overflow-tooltip":""})]),_:1},8,["data","cell-style","header-cell-style"])),[[C,i.value]])]),o("div",U,[a(x,{background:"",layout:"prev, pager, next",total:g.value,"current-page":n.value,"page-size":m,onCurrentChange:w},null,8,["total","current-page"])])])}}});const Y=A(j,[["__scopeId","data-v-085e6d3c"]]);export{Y as default};

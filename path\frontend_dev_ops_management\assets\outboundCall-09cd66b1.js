/* empty css             */import{C as x,c as V,h as k}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as E,r as s,A as B,o as h,e as L,f as c,g as a,K as N,q as z,w as u,n as b,aD as D,E as M,L as S,M as T,N as I,h as P,t as v,C as R,O as q}from"./index-d4ffb1a1.js";import{_ as A}from"./_plugin-vue_export-helper-c27b6911.js";const K={class:"voice-call-record"},O={class:"search-header"},U={class:"pagination-wrapper"},p=10,j=E({__name:"outboundCall",setup(F){const r=s(!1),g=s(0),n=s(1),i=s(""),m=s([]);function f(){n.value=1,d()}async function d(){r.value=!0;const o={search:i.value,page:n.value,page_size:p},{data:t}=await D(o),{state:e,msg:_}=t;e=="success"?(m.value=t.data.results,g.value=t.data.count):M.error(_),r.value=!1}function w(o){n.value=o,d()}return B(()=>{d()}),(o,t)=>{const e=q,_=S,C=T,y=I;return h(),L("div",K,[c("div",O,[a(x,{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=l=>i.value=l),placeholder:"搜索被叫号码、主叫号码、外呼结果备注",onClick:f},null,8,["modelValue"])]),c("div",null,[N((h(),z(_,{data:m.value,border:"","cell-style":b(V),"header-cell-style":b(k),class:"record-table"},{default:u(()=>[a(e,{type:"index",label:"序号",width:"60",align:"center"},{default:u(({$index:l})=>[P(v(p*(n.value-1)+l+1),1)]),_:1}),a(e,{prop:"call_start_time",label:"呼叫开始时间",align:"center",width:"190"}),a(e,{prop:"called_number",label:"被叫号码",align:"center",width:"130"}),a(e,{prop:"caller_number",label:"主叫号码",align:"center",width:"130"}),a(e,{prop:"call_status_cn",label:"呼叫状态",align:"center",width:"90"},{default:u(({row:l})=>[c("span",{class:R({"text-success":l.call_status_cn==="已接通","text-warning":l.call_status_cn==="未接通","text-danger":l.call_status_cn!=="已接通"&&l.call_status_cn!=="未接通"})},v(l.call_status_cn),3)]),_:1}),a(e,{prop:"creditor_name",label:"债权人名称",align:"center","min-width":"120"}),a(e,{prop:"debtor_name",label:"债务人名称",align:"center",width:"120"}),a(e,{prop:"call_duration_display",label:"通话时长",align:"center",width:"120"}),a(e,{prop:"call_end_time",label:"呼叫结束时间",align:"center",width:"190"}),a(e,{prop:"task_batch_id",label:"外呼任务批次号",align:"center",width:"150"}),a(e,{prop:"recording_file_name",label:"录音文件名",align:"center",width:"150"}),a(e,{prop:"call_result_notes",label:"外呼结果备注",align:"center",width:"140","show-overflow-tooltip":""})]),_:1},8,["data","cell-style","header-cell-style"])),[[y,r.value]])]),c("div",U,[a(C,{background:"",layout:"prev, pager, next",total:g.value,"current-page":n.value,"page-size":p,onCurrentChange:w},null,8,["total","current-page"])])])}}});const Y=A(j,[["__scopeId","data-v-9cb201b0"]]);export{Y as default};

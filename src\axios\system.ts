import { defaultInstance } from ".";

export const getDepartments = (data:any) => defaultInstance.get('/department/', { params: data })

export const addDepartments = (data:any) => defaultInstance.post('/department/', data)

export const putDepartments = (data:any, id: number) => defaultInstance.patch(`/department/${id}/`, data)

export const deleteDepartments = (id: number) => defaultInstance.del(`/department/${id}/`)

export const getApplications = (data:any) => defaultInstance.get(`/application/`, { params: data })

export const getPermissions = (data:any) => defaultInstance.get(`/permission/`, { params: data })

export const getPermissionsById = (id:number) => defaultInstance.get(`/permission/${id}`)

export const getPermissionsStructure = (data: any) => defaultInstance.get(`/permission_structure/`, { params: data })

export const getRole = (data:any) => defaultInstance.get(`/role/`, { params: data })
export const addRole = (data:any) => defaultInstance.post(`/role/`, data)
export const editRole = (data:any, id:number) => defaultInstance.patch(`/role/${id}/`, data)
export const deleteRole = (id:number) => defaultInstance.del(`/role/${id}/`)

export const getApplication = (data:any) => defaultInstance.get('/application/', { params: data })

export const getUser = (data:any) => defaultInstance.get('/user/', { params: data })

export const addUser = (data:any) => defaultInstance.post('/user/', data)

export const patchUser = (data:any, id: number) => defaultInstance.patch(`/user/${id}/`, data)

export const getUserDetail = (id:number) => defaultInstance.get(`/user/${id}/`)

export const deleteUser = (id: number) => defaultInstance.del(`/user/${id}/`)

// 操作日志相关接口
export const addOperationLog = (data: any) => defaultInstance.post('/user/operation_log/', data)

// (调解管理)案件跟踪（发起）
export const batchUpdateStatus = (data: any) => defaultInstance.post('/mediation_management/mediation_case/batch_update_status/', data)
// (调解管理)案件跟踪（调解信息）
export const getMediationCaseContent = (case_number: number) => defaultInstance.get(`/mediation_management/mediation_case/${case_number}/content/`)
// (调解管理)案件跟踪（调解方案）
export const getMediationCasePlanConfig = (case_number: number) => defaultInstance.get(`/mediation_management/mediation_case/${case_number}/plan_config/`)

// 案件状态下拉框
export const getStatusChoices = () => defaultInstance.get(`/mediation_management/mediation_case/status_choices/`)

// (调解管理)调解信息列表
export const getMediationCase = (data: any) => defaultInstance.get('/mediation_management/mediation_case/', { params: data })
// (调解管理)调解信息新增
export const addMediationCase =  (data: any) => defaultInstance.post('/mediation_management/mediation_case/', data,{
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// (调解管理)调解信息详情
export const getMediationCaseDetail = (id: number) => defaultInstance.get(`/mediation_management/mediation_case/${id}/`)
// (调解管理)调解信息编辑
export const editMediationCase = (data: any, id: number) => defaultInstance.put(`/mediation_management/mediation_case/${id}/`, data,{
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// (调解管理)调解信息-->表达式编辑:失去焦点调用，接口返回的data值赋值给内容预览preview)
export const expressionCalculation = (data: any) => defaultInstance.post('/data_governance/asset_package_management/expression_calculation/', data)
// (调解管理)调解信息-->tab资产包编辑
export const updateAttachmentsMediation = (data: any, id: number) => defaultInstance.post(`/data_governance/asset_package_management/${id}/update_attachments_and_mediation/`, data,{
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// (调解管理)调解信息删除
export const deleteMediationCase = (id: number) => defaultInstance.del(`/mediation_management/mediation_case/${id}/`)

// (调解管理)调解方案列表
export const getMediationPlan = (data: any) => defaultInstance.get('/mediation_management/mediation_plan/', { params: data })
// (调解管理)调解方案下拉框数据（方案状态、审批状态）
export const getStatusChoicesOptions = () => defaultInstance.get('/mediation_management/mediation_plan/status_choices/')
//  (调解管理)调解方案（调解案件tab）审批
export const mediationPlanApprove = (data: any, id: number) => defaultInstance.post(`/mediation_management/mediation_plan/${id}/approve/`, data)
// (调解管理)调解方案新增
export const addMediationPlan = (data: any) => defaultInstance.post('/mediation_management/mediation_plan/', data)
// (调解管理)调解方案详情
export const getMediationPlanDetail = (id: number) => defaultInstance.get(`/mediation_management/mediation_plan/${id}/`)
// (调解管理)调解方案编辑
export const editMediationPlan = (data: any, id: number) => defaultInstance.put(`/mediation_management/mediation_plan/${id}/`, data)
// (调解管理)调解方案删除
export const deleteMediationPlan = (id: number) => defaultInstance.del(`/mediation_management/mediation_plan/${id}/`)

// (调解管理)人员调度：获取调解员下拉框
export const getMediatorsOptions = () => defaultInstance.get('/user/mediators/')
// (调解管理)人员调度：编辑保存
export const  editUpdateMediatorn = (data: any,id: number) => defaultInstance.put(`/mediation_management/mediation_case/${id}/update_mediator/`,data)


// 查询债权人
export const getCreditor = (data: any) => defaultInstance.get('/counterparty/creditor_basic_info/', { params: data })
// 获取债权人下拉框数据(类型和证件类型)
export const getCreditorOptions = () => defaultInstance.get('/counterparty/creditor_basic_info/choices/')
// 新增债权人
export const addCreditor = (data: any) => defaultInstance.post('/counterparty/creditor_basic_info/', data)
// 编辑债权人
export const editCreditor = (data: any, id: number) => defaultInstance.patch(`/counterparty/creditor_basic_info/${id}/`, data)
// 获取债权人详情
export const getCreditorDetail = (id: number) => defaultInstance.get(`/counterparty/creditor_basic_info/${id}/`)
// 删除债权人
export const deleteCreditor = (id: number) => defaultInstance.del(`/counterparty/creditor_basic_info/${id}/`)


// 查询债务人
export const getDebtor = (data: any) => defaultInstance.get('/counterparty/debtor_basic_info/', { params: data })
// 获取债务人下拉框数据(类型和证件类型)
export const getDebtorOptions = () => defaultInstance.get('/counterparty/debtor_basic_info/choices/')
// 新增债务人
export const addDebtor = (data: any) => defaultInstance.post('/counterparty/debtor_basic_info/', data)
// 编辑债务人
export const editDebtor = (data: any, id: number) => defaultInstance.patch(`/counterparty/debtor_basic_info/${id}/`, data)
// 获取债务人详情
export const getDebtorDetail = (id: number) => defaultInstance.get(`/counterparty/debtor_basic_info/${id}/`)
// 删除债务人
export const deleteDebtor = (id: number) => defaultInstance.del(`/counterparty/debtor_basic_info/${id}/`)


// 新增调解案例
export const getCaseDisplay = (data: any) => defaultInstance.get('/case_display/case_display/', { params: data })
// 查询调解案例
export const addCaseDisplay = (data: any) => defaultInstance.post('/case_display/case_display/', data)
// 编辑调解案例
export const editCaseDisplay = (data: any, id: number) => defaultInstance.patch(`/case_display/case_display/${id}/`, data)
// 删除调解案例
export const deleteCaseDisplay = (id: number) => defaultInstance.del(`/case_display/case_display/${id}/`)

// 查询投诉建议
export const getFeedback = (data: any) => defaultInstance.get('/feedback/feedback/', { params: data })
// 查看详情投诉建议
// export const getFeedbackDetail = (id: number) => defaultInstance.get(`/feedback/feedback/${id}/`)
// 编辑投诉建议
export const editFeedback = (data: any, id: number) => defaultInstance.post(`/feedback/feedback/${id}/handle/`, data)

// 外呼管理（语音外呼列表）
export const getVoiceCallRecords = (data: any) => defaultInstance.get('/outbound_communication/voice_call_records/', { params: data })
// 外呼管理（短信发送列表）
export const getSmsRecords = (data: any) => defaultInstance.get('/outbound_communication/sms_records/', { params: data })

// (数据治理)查询字段配置列表
export const getFieldConfig = (data: any) => defaultInstance.get('/data_governance/asset_package_field_config/', { params: data })
// (数据治理)获取字段类型下拉框
export const getFieldTypes = () => defaultInstance.get('/data_governance/asset_package_field_config/field_types/')
// (数据治理)新增字段配置
export const addFieldConfig = (data: any) => defaultInstance.post('/data_governance/asset_package_field_config/', data)
// (数据治理)编辑字段配置
export const editFieldConfig = (data: any, id: number) => defaultInstance.patch(`/data_governance/asset_package_field_config/${id}/`, data)
// (数据治理)删除字段配置
export const deleteFieldConfig = (id: number) => defaultInstance.del(`/data_governance/asset_package_field_config/${id}/`)
// (数据治理) 上移下移批量保存数据
export const bulkUpdateFieldConfig = (data: any) => defaultInstance.post('/data_governance/asset_package_field_config/bulk_update/', data)

// （数据治理）获取数据导入列表
export const getDataImportList = (data: any) => defaultInstance.get('/data_governance/asset_package_management/', { params: data })
// （数据治理）获取数据导入详情
export const getDataImportDetail = (id: number) => defaultInstance.get(`/data_governance/asset_package_management/${id}/`)
// （数据治理）新增数据导入
export const addDataImport = (data: any) => defaultInstance.post('/data_governance/asset_package_management/', data)
// （数据治理）编辑数据导入
export const editDataImport = (data: any, id: number) => defaultInstance.put(`/data_governance/asset_package_management/${id}/`, data)
// （数据治理）编辑(债务人下拉框)
export const getDebtorFieldChoices = () => defaultInstance.get('/data_governance/asset_package_management/debtor_field_choices/')
// （数据治理）删除数据导入
export const deleteDataImport = (id: number) => defaultInstance.del(`/data_governance/asset_package_management/${id}/`)
// （数据治理）重新上传
export const reprocessExcel = (id: number,data: any) => defaultInstance.post(`/data_governance/asset_package_management/${id}/reprocess_excel/`, data)
// （数据治理）原始数据预览
export const previewRawData = (package_id: number) => defaultInstance.get(`/data_governance/asset_package_management/${package_id}/preview_raw_data/`)
// （数据治理）运营数据预览
export const previewMappedData = (package_id: number) => defaultInstance.get(`/data_governance/asset_package_management/${package_id}/preview_mapped_data/`)

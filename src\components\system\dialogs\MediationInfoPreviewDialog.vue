<template>
  <CustomDialog 
    :title="'预览调解信息'"
    :visible="visible" 
    width="800px"
    :markclose="true"
    @update:visible="handleClose">
    
    <div class="preview-content" v-if="data">
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">调解案件号：</span>
            <span class="value">{{ data.case_number || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">案件状态：</span>
            <span class="value">{{ data.case_status_cn || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债权人：</span>
            <span class="value">{{ data.creditor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债务人：</span>
            <span class="value">{{ data.debtor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">资产包名称：</span>
            <span class="value">{{ data.asset_package_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">调解员：</span>
            <span class="value">{{ data.mediator_name || '-' }}</span>
          </div>
        </div>
      </div>
 
      <!-- 调解信息配置 - 从 getMediationCaseContent 接口获取 -->
      <div class="info-section" v-if="data.mediation_config && data.mediation_config.length > 0">
        <h3 class="section-title">调解信息配置</h3>
        <div class="config-list">
          <div
            v-for="(config, index) in data.mediation_config"
            :key="index"
            class="config-item">
            <div class="config-title">{{ config.title || config.field_name || `字段${index + 1}` }}</div>
            <div class="config-value">{{ config.value || config.field_value || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 相关文件 - 支持 attachments 数据结构 -->
      <div class="info-section" v-if="data.attachments && data.attachments.length > 0">
        <h3 class="section-title">相关文件</h3>
        <div class="file-list">
          <div v-for="(file, index) in data.attachments" :key="index" class="file-item">
            <div class="file-info">
              <span class="file-name">{{ file.name }}</span>
              <!-- <span class="file-type">{{ file.type || 'unknown' }}</span> -->
            </div>
            <!-- <div class="file-actions">
              <a :href="file.download_url" target="_blank" class="download-link">下载</a>
            </div> -->
          </div>
        </div>
      </div>

      <!-- 兼容原有的 file_cn 字段 -->
      <div class="info-section" v-else-if="data.file_cn">
        <h3 class="section-title">相关文件</h3>
        <div class="file-content">
          <pre>{{ data.file_cn }}</pre>
        </div>
      </div>
    </div>

    <div class="empty-state" v-else>
      <p>暂无数据</p>
    </div>

    <!-- 底部按钮 -->
    <!-- <div class="dialog-footer">
      <CustomButton @click="handleClose" :height="34">
        <i class="jt-20-delete"></i>关闭
      </CustomButton>
    </div> -->
  </CustomDialog>
</template>

<script lang="ts" setup>
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

interface Props {
  visible: boolean
  data: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null
})

const emit = defineEmits<Emits>()

function handleClose() {
  emit('update:visible', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.preview-content {
  .info-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      
      .info-item {
        display: flex;
        align-items: center;
        
        .label {
          min-width: 120px;
          font-weight: 500;
          color: #666;
        }
        
        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }
    
    .config-list {
      .config-item {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f9fafb;
        border-radius: 6px;
        border-left: 4px solid #3b82f6;
        
        .config-title {
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
        
        .config-value {
          color: #6b7280;
          word-break: break-all;
        }
      }
    }

    .file-list {
      .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: #f5f7fa;
        border-radius: 4px;
        margin-bottom: 8px;

        .file-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .file-name {
            font-weight: 500;
            color: #333;
          }

          .file-type {
            padding: 2px 8px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
            font-size: 12px;
          }
        }

        .file-actions {
          .download-link {
            color: #1976d2;
            text-decoration: none;
            padding: 4px 12px;
            border: 1px solid #1976d2;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;

            &:hover {
              background: #1976d2;
              color: white;
            }
          }
        }
      }
    }

    .file-content {
      background-color: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 16px;
      
      pre {
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
        font-family: inherit;
        color: #374151;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>

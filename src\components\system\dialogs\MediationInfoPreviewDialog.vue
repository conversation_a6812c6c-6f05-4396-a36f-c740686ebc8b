<template>
  <CustomDialog 
    :title="'预览调解信息'"
    :visible="visible" 
    width="800px"
    :markclose="true"
    @update:visible="handleClose">
    
    <div class="preview-content" v-if="data">
      <div class="info-section">
        <!-- <h3 class="section-title">基本信息</h3> -->
        <div class="info-grid">
          <div class="info-item">
            <span class="label">调解案件号：</span>
            <span class="value">{{ data.case_number || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">案件状态：</span>
            <span class="value">{{ data.case_status_cn || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债权人：</span>
            <span class="value">{{ data.creditor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债务人：</span>
            <span class="value">{{ data.debtor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">资产包名称：</span>
            <span class="value">{{ data.asset_package_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">调解员：</span>
            <span class="value">{{ data.mediator_name || '-' }}</span>
          </div>
        </div>
      </div>
 
      <div class="info-section" v-if="data.mediation_config && data.mediation_config.length > 0">
        <h3 class="section-title">调解信息配置</h3>
        <div class="config-list">
          <div 
            v-for="(config, index) in data.mediation_config" 
            :key="index"
            class="config-item">
            <div class="config-title">{{ config.title || `字段${index + 1}` }}</div>
            <div class="config-value">{{ config.value || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 相关文件 -->
      <div class="info-section" v-if="data.file_cn">
        <h3 class="section-title">相关文件</h3>
        <div class="file-content">
          <pre>{{ data.file_cn }}</pre>
        </div>
      </div>
    </div>

    <div class="empty-state" v-else>
      <p>暂无数据</p>
    </div>

    <!-- 底部按钮 -->
    <!-- <div class="dialog-footer">
      <CustomButton @click="handleClose" :height="34">
        <i class="jt-20-delete"></i>关闭
      </CustomButton>
    </div> -->
  </CustomDialog>
</template>

<script lang="ts" setup>
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

interface Props {
  visible: boolean
  data: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null
})

const emit = defineEmits<Emits>()

function handleClose() {
  emit('update:visible', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.preview-content {
  max-height: 600px;
  overflow-y: auto;
  
  .info-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      
      .info-item {
        display: flex;
        align-items: center;
        
        .label {
          min-width: 120px;
          font-weight: 500;
          color: #666;
        }
        
        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }
    
    .config-list {
      .config-item {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f9fafb;
        border-radius: 6px;
        border-left: 4px solid #3b82f6;
        
        .config-title {
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
        
        .config-value {
          color: #6b7280;
          word-break: break-all;
        }
      }
    }
    
    .file-content {
      background-color: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 16px;
      
      pre {
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
        font-family: inherit;
        color: #374151;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>

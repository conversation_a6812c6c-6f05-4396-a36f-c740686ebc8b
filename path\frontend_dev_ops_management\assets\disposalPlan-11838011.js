/* empty css             */import{C as Be,c as Me,h as Le}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 *//* empty css                       */import{C as Q}from"./CustomButton-777ba9d4.js";/* empty css                  *//* empty css                 *//* empty css                     */import{d as we,r as h,v as se,c as oe,o as r,q as A,w as u,f as a,g as n,e as b,B as z,F as j,h as V,t as O,C as ge,Z as ye,G as Y,S as je,E as P,_ as ze,a1 as ke,X as Ee,U as Pe,k as Se,j as Ae,l as Ue,a2 as Oe,a3 as Te,a4 as Re,p as $e,m as Ve,a as qe,A as Ke,a9 as Xe,I as He,aa as Ge,ab as Je,ac as Ie,ad as Ze,ae as Qe,a7 as We,M as Ye,K as Ne,n as he,af as ea,O as aa,L as ta,a8 as sa,N as la}from"./index-d4ffb1a1.js";import{_ as xe}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{F as le}from"./type-bba1e229.js";import{_ as Ce}from"./_plugin-vue_export-helper-c27b6911.js";import{D as oa}from"./DeleteConfirmDialog-5eb37e5a.js";/* empty css                                                                            */const X=D=>($e("data-v-c395d86c"),D=D(),Ve(),D),na={class:"add-plan-content"},ia={class:"dynamic-fields-section"},ca={class:"section-header"},ua=X(()=>a("h3",null,"方案配置",-1)),ra=X(()=>a("i",{class:"jt-20-addition"},null,-1)),da={key:0,class:"fields-list"},pa={class:"field-header"},_a={class:"field-index"},va=X(()=>a("i",{class:"jt-20-remove"},null,-1)),ma={class:"field-config"},ga={class:"config-row"},fa=X(()=>a("label",{class:"config-label"},"字段类型：",-1)),ha={style:{display:"flex","align-items":"center"}},ba={class:"config-row"},ya=X(()=>a("label",{class:"config-label"},"字段标题：",-1)),ka=X(()=>a("label",{class:"config-label"},"逻辑类型：",-1)),wa={class:"expression-section"},$a=X(()=>a("label",{class:"config-label"},"表达式编辑：",-1)),Va={class:"custom-expression-editor"},Ca={class:"expression-input-container"},Da={key:0,class:"variable-dropdown"},Ea={class:"dropdown-header"},Pa=X(()=>a("span",null,"选择变量：",-1)),Sa={class:"variable-search"},Aa={class:"variable-list"},Ua=["onClick"],Oa={class:"variable-name"},Ta={key:0,class:"no-results"},xa={key:0,class:"expression-preview"},Fa=X(()=>a("span",{class:"preview-label"},"表达式预览：",-1)),Ma=["innerHTML"],La={class:"preview-section"},Na={class:"preview-content"},ja={key:1,class:"empty-fields"},Ra=X(()=>a("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),Ba=[Ra],za={class:"btns-group"},qa=X(()=>a("i",{class:"jt-20-ensure"},null,-1)),Ka=X(()=>a("i",{class:"jt-20-delete"},null,-1)),Xa=we({__name:"AddDisposalPlan",props:{showDialog:{type:Boolean},currentMode:{},mediationCaseOptions:{},assetPackageOptions:{},assetPackageData:{}},emits:["close","ensure"],setup(D,{emit:N}){const $=D,C=h(),R=h(),y=h(!1),v=h({mediation_case:"",plan_name:"",asset_package:""}),k=h([]),M=h([]);se(()=>$.showDialog,e=>{e&&q()});async function q(){let e;if(C.value=null,F.value==="mediationCases"){if(v.value.mediation_case){const s=v.value.mediation_case;$.assetPackageData&&$.assetPackageData.forEach(p=>{p.id==s&&(e=p.case_number)});const{data:_}=await je({page:1,page_size:1e3,search:e,package_status:"available"}),{state:t,msg:l}=_;if(t==="success"){const p=_.data.results;p.length>0&&(M.value=p[0].mapped_field_names.map(g=>({code:g,name:g})),C.value=p[0].asset_package)}else P.error(l)}}else if(v.value.asset_package){const{data:s}=await ze(Number(v.value.asset_package)),{state:_,msg:t}=s;if(_==="success"){const l=s.data;M.value=l.mapped_field_names.map(p=>({code:p,name:p})),C.value=l.id||Number(v.value.asset_package)}else P.error(t)}}se(()=>v.value.mediation_case,e=>{e&&F.value==="mediationCases"&&q()}),se(()=>v.value.asset_package,e=>{e&&F.value==="asset"&&q()});const E=h({}),S=h({}),T=h({}),B=h({}),x=[{label:"文本类型",value:le.TEXTAREA,icon:"jt-24-edit"},{label:"日期类型",value:le.DATE,icon:"jt-24-calendar"},{label:"金额类型",value:le.AMOUNT,icon:"jt-24-money"}],F=oe(()=>$.currentMode||"mediationCases"),G=oe(()=>"新增方案"),J=oe(()=>{const e={plan_name:[{required:!0,message:"请输入方案名称",trigger:"blur"}]};return F.value==="asset"?{...e,asset_package:[{required:!0,message:"请选择资产包",trigger:"change"}]}:{...e,mediation_case:[{required:!0,message:"请选择调解案件",trigger:"change"}]}});se(()=>$.showDialog,I);function I(e){e&&K()}function K(){F.value==="mediationCases"?v.value.mediation_case="":v.value.asset_package="",v.value.plan_name="",k.value=[]}function W(){N("close")}function ne(){const e=Date.now().toString(36),s=Math.random().toString(36).substring(2,11);return`field_${e}_${s}`}function re(){const e={id:ne(),title:"",type:le.TEXTAREA,logic_type:"result_calculation",expression:"",preview:""};k.value.push(e)}function de(e){k.value.splice(e,1)}function ie(e,s){const _=String(e);if(k.value[s]){k.value[s].logic_type=_;const t=k.value[s].expression;t&&t.trim()&&ce(t,s)}}async function ce(e,s){const _=k.value[s],t=(_==null?void 0:_.logic_type)||"text_format",{data:l}=await ke({asset_package_id:C.value,expression:e,logic_type:t}),p=l.data;k.value[s]&&(k.value[s].preview=String(p))}function pe(e,s){k.value[s]&&(k.value[s].expression=e)}async function ee(e,s){const t=e.target.value;if(console.log("失焦事件 - 输入框显示值:",t),!k.value[s])return;const l=k.value[s];if(l.expression=t,te(s),!t.trim()){l.preview="";return}const p=l.logic_type||"result_calculation",g={asset_package_id:C.value,expression:t,logic_type:p},m=await ke(g);m.data.state==="success"?l.preview=String(m.data.data):l.preview=m.data.msg}function Z(e,s){const _=e.target;T.value[s]=_.selectionStart||0,e.key==="@"?(setTimeout(()=>{T.value[s]=_.selectionStart||0},0),E.value[s]=!0,S.value[s]=M.value,B.value[s]=""):e.key==="Escape"&&te(s)}function ae(e,s){if(!e||e.trim()==="")S.value[s]=M.value;else{const _=M.value.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.code.toLowerCase().includes(e.toLowerCase()));S.value[s]=_}}function te(e){E.value[e]=!1,S.value[e]=[]}function _e(e,s){if(!k.value[s])return;const _=k.value[s],t=_.expression||"",l=T.value[s]||t.length,p=t.substring(0,l),g=t.substring(l);let m=p;const U=p.lastIndexOf("@");if(U!==-1){const d=p.substring(U+1);(d.trim()===""||/^[\s]*$/.test(d))&&(m=p.substring(0,U))}const f=`{${e.name}}`,o=m+f+g;_.expression=o,te(s)}function ve(e){return!e||e.trim()===""?"请输入表达式，支持中文、数字、运算符（+ - * /），输入@可选择变量":""}function ue(e){return e?e.replace(/\{([^}]+)\}/g,'<span class="variable-highlight">{$1}</span>'):""}function me(){for(let e=0;e<k.value.length;e++){const s=k.value[e];if(!s.title.trim())return P.error(`第${e+1}个字段的标题不能为空`),!1;if(!s.expression||String(s.expression).trim()==="")return P.error(`${s.expression}是必填字段，请填写内容`),!1}return!0}async function c(){if(R.value){y.value=!0;try{if(!await new Promise(_=>{R.value.validate(t=>{_(t)})})||!me())return;if(k.value.length===0){P.error("请至少添加一个字段");return}const s={plan_name:v.value.plan_name,plan_config:k.value,...F.value==="asset"&&{asset_package:v.value.asset_package},...F.value==="mediationCases"&&{mediation_case:v.value.mediation_case}};N("ensure",s)}finally{y.value=!1}}}return(e,s)=>{const _=Ee,t=Pe,l=Se,p=Ae,g=Ue,m=Oe,U=Te,f=Re;return r(),A(xe,{visible:e.showDialog,"onUpdate:visible":W,width:"1200px",title:G.value},{default:u(()=>[a("div",na,[n(g,{ref_key:"planFormRef",ref:R,model:v.value,rules:J.value,"label-width":"110px"},{default:u(()=>[F.value==="asset"?(r(),A(l,{key:0,label:"资产包",prop:"asset_package"},{default:u(()=>[n(t,{modelValue:v.value.asset_package,"onUpdate:modelValue":s[0]||(s[0]=o=>v.value.asset_package=o),filterable:"",placeholder:"请选择资产包",style:{width:"100%"}},{default:u(()=>[(r(!0),b(j,null,z(e.assetPackageOptions,o=>(r(),A(_,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(r(),A(l,{key:1,label:"调解案件",prop:"mediation_case"},{default:u(()=>[n(t,{modelValue:v.value.mediation_case,"onUpdate:modelValue":s[1]||(s[1]=o=>v.value.mediation_case=o),filterable:"",placeholder:"请选择调解案件",style:{width:"100%"}},{default:u(()=>[(r(!0),b(j,null,z(e.mediationCaseOptions,o=>(r(),A(_,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),n(l,{label:"方案名称",prop:"plan_name"},{default:u(()=>[n(p,{modelValue:v.value.plan_name,"onUpdate:modelValue":s[2]||(s[2]=o=>v.value.plan_name=o),placeholder:"例如方案一、方案二"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",ia,[a("div",ca,[ua,n(Q,{onClick:re,height:34,"btn-type":"blue"},{default:u(()=>[ra,V("添加字段 ")]),_:1})]),k.value.length>0?(r(),b("div",da,[(r(!0),b(j,null,z(k.value,(o,d)=>(r(),b("div",{key:o.id||`field-${d}`,class:"field-item"},[a("div",pa,[a("span",_a,"字段 "+O(d+1),1),n(Q,{onClick:w=>de(d),height:28,"btn-type":"red"},{default:u(()=>[va,V("删除 ")]),_:2},1032,["onClick"])]),a("div",ma,[a("div",ga,[fa,n(t,{modelValue:o.type,"onUpdate:modelValue":w=>o.type=w,style:{width:"280px"}},{default:u(()=>[(r(),b(j,null,z(x,w=>n(_,{key:w.value,label:w.label,value:w.value},{default:u(()=>[a("span",ha,[a("i",{class:ge(w.icon),style:{"margin-right":"8px"}},null,2),V(" "+O(w.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),a("div",ba,[ya,n(p,{modelValue:o.title,"onUpdate:modelValue":w=>o.title=w,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])])]),(r(),b("div",{class:"logic-type-section",key:`logic-type-${o.id}-${d}`},[ka,(r(),A(U,{modelValue:o.logic_type,"onUpdate:modelValue":w=>o.logic_type=w,class:"logic-type-radio-group",onChange:w=>ie(w,d),key:`radio-group-${o.id}-${d}`},{default:u(()=>[(r(),A(m,{label:"text_format",size:"small",key:`text-format-${o.id}-${d}`},{default:u(()=>[V(" 文本格式化 ")]),_:2},1024)),(r(),A(m,{label:"result_calculation",size:"small",key:`result-calculation-${o.id}-${d}`},{default:u(()=>[V(" 结果运算 ")]),_:2},1024))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]))])),a("div",wa,[$a,a("div",Va,[a("div",Ca,[n(p,{modelValue:o.expression,"onUpdate:modelValue":w=>o.expression=w,onInput:w=>pe(w,d),type:"textarea",rows:3,placeholder:ve(o.expression),onBlur:w=>ee(w,d),onKeydown:w=>Z(w,d),class:"expression-textarea"},null,8,["modelValue","onUpdate:modelValue","onInput","placeholder","onBlur","onKeydown"]),E.value[d]?(r(),b("div",Da,[a("div",Ea,[Pa,n(f,{size:"small",text:"",onClick:w=>te(d)},{default:u(()=>[V("×")]),_:2},1032,["onClick"])]),a("div",Sa,[n(p,{modelValue:B.value[d],"onUpdate:modelValue":w=>B.value[d]=w,size:"small",placeholder:"搜索变量名称...",onInput:w=>ae(B.value[d]||"",d),clearable:""},null,8,["modelValue","onUpdate:modelValue","onInput"])]),a("div",Aa,[(r(!0),b(j,null,z(S.value[d]||M.value,w=>(r(),b("div",{key:w.code,onClick:ye(i=>_e(w,d),["stop"]),onMousedown:s[3]||(s[3]=ye(()=>{},["prevent"])),class:"variable-item"},[a("span",Oa,O(w.name),1)],40,Ua))),128)),(S.value[d]||M.value).length===0?(r(),b("div",Ta," 未找到匹配的变量 ")):Y("",!0)])])):Y("",!0)])])]),o.expression&&o.expression.trim()?(r(),b("div",xa,[Fa,a("span",{class:"preview-content",innerHTML:ue(o.expression)},null,8,Ma)])):Y("",!0),a("div",La,[V(" 内容预览："),a("div",Na,O(o.preview),1)])]))),128))])):(r(),b("div",ja,Ba))]),a("div",za,[n(Q,{onClick:c,loading:y.value,height:34,"btn-type":"blue"},{default:u(()=>[qa,V("确认 ")]),_:1},8,["loading"]),n(Q,{onClick:W,height:34},{default:u(()=>[Ka,V("取消 ")]),_:1})])])]),_:1},8,["visible","title"])}}});const Ha=Ce(Xa,[["__scopeId","data-v-c395d86c"]]),H=D=>($e("data-v-90d6a967"),D=D(),Ve(),D),Ga={class:"edit-plan-content"},Ja={class:"dynamic-fields-section"},Ia={class:"section-header"},Za=H(()=>a("h3",null,"字段配置",-1)),Qa=H(()=>a("i",{class:"jt-20-addition"},null,-1)),Wa={key:0,class:"fields-list"},Ya={class:"field-header"},et={class:"field-index"},at=H(()=>a("i",{class:"jt-20-remove"},null,-1)),tt={class:"field-config"},st={class:"config-row"},lt=H(()=>a("label",{class:"config-label"},"字段类型：",-1)),ot={style:{display:"flex","align-items":"center"}},nt={class:"config-row"},it=H(()=>a("label",{class:"config-label"},"字段标题：",-1)),ct=H(()=>a("label",{class:"config-label"},"逻辑类型：",-1)),ut={class:"expression-section"},rt=H(()=>a("label",{class:"config-label"},"表达式编辑：",-1)),dt={class:"custom-expression-editor"},pt={class:"expression-input-container"},_t={key:0,class:"variable-dropdown"},vt={class:"dropdown-header"},mt=H(()=>a("span",null,"选择变量：",-1)),gt={class:"variable-search"},ft={class:"variable-list"},ht=["onClick"],bt={class:"variable-name"},yt={key:0,class:"no-results"},kt={key:0,class:"expression-preview"},wt=H(()=>a("span",{class:"preview-label"},"表达式预览：",-1)),$t=["innerHTML"],Vt={class:"preview-section"},Ct={class:"preview-content"},Dt={key:1,class:"empty-fields"},Et=H(()=>a("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),Pt=[Et],St={class:"btns-group"},At=H(()=>a("i",{class:"jt-20-ensure"},null,-1)),Ut=H(()=>a("i",{class:"jt-20-delete"},null,-1)),Ot=we({__name:"EditDisposalPlan",props:{showDialog:{type:Boolean},planData:{},currentMode:{},mediationCaseOptions:{},assetPackageOptions:{}},emits:["close","ensure"],setup(D,{emit:N}){const $=D,C=h(),R=h(!1),y=h({asset_package:null,mediation_case:null,plan_name:""}),v=h([]),k=h([]);se(()=>$.showDialog,c=>{c&&$.planData&&M()});async function M(){k.value=$.planData.mapped_field_names.map(c=>({code:c,name:c}))}const q=h({}),E=h({}),S=h({}),T=h({}),B=[{label:"文本类型",value:le.TEXTAREA,icon:"jt-24-edit"},{label:"日期类型",value:le.DATE,icon:"jt-24-calendar"},{label:"金额类型",value:le.AMOUNT,icon:"jt-24-money"}],x=oe(()=>$.currentMode||"mediationCases"),F=oe(()=>x.value==="asset"?"编辑资产包方案":"编辑调解案件方案"),G=oe(()=>({plan_name:[{required:!0,message:"请输入方案名称",trigger:"blur"}]}));se(()=>$.showDialog,J),se(()=>$.planData,I,{deep:!0});function J(c){c&&I($.planData)}function I(c){c&&(x.value==="asset"?y.value.asset_package=c.asset_package:y.value.mediation_case=c.mediation_case,y.value.plan_name=c.plan_name,v.value=c.plan_config?JSON.parse(JSON.stringify(c.plan_config)).map(e=>(e.logic_type||(e.logic_type="result_calculation"),e.expression||(e.expression=""),e.preview||(e.preview=""),(!e.id||typeof e.id!="string"||e.id.trim()==="")&&(e.id=W()),e)):[])}function K(){N("close")}function W(){const c=Date.now().toString(36),e=Math.random().toString(36).substring(2,11);return`field_${c}_${e}`}function ne(){const c={id:W(),title:"",type:le.TEXTAREA,logic_type:"result_calculation",expression:"",preview:""};v.value.push(c)}function re(c){v.value.splice(c,1)}function de(c,e){const s=String(c);if(v.value[e]){v.value[e].logic_type=s;const _=v.value[e].expression;_&&_.trim()&&ce(_,e)}}function ie(c,e){v.value[e]&&(v.value[e].expression=c)}async function ce(c,e){var g,m;const s=v.value[e],_=(s==null?void 0:s.logic_type)||"result_calculation",t={asset_package_id:(g=$.planData)!=null&&g.asset_package?Number($.planData.asset_package):void 0,mediation_case_id:(m=$.planData)!=null&&m.mediation_case?Number($.planData.mediation_case):void 0,expression:c,logic_type:_},{data:l}=await ke(t),p=l.data;v.value[e]&&(v.value[e].preview=String(p))}async function pe(c,e){var m,U;const _=c.target.value;if(!v.value[e])return;const t=v.value[e];if(t.expression=_,ae(e),!_.trim()){t.preview="";return}const l=t.logic_type||"result_calculation",p={asset_package_id:(m=$.planData)!=null&&m.asset_package?Number($.planData.asset_package):void 0,mediation_case_id:(U=$.planData)!=null&&U.mediation_case?Number($.planData.mediation_case):void 0,expression:_,logic_type:l},g=await ke(p);g.data.state==="success"?t.preview=String(g.data.data):t.preview=g.data.msg}function ee(c,e){const s=c.target;S.value[e]=s.selectionStart||0,c.key==="@"?(setTimeout(()=>{S.value[e]=s.selectionStart||0},0),q.value[e]=!0,E.value[e]=k.value,T.value[e]=""):c.key==="Escape"&&ae(e)}function Z(c,e){if(!c||c.trim()==="")E.value[e]=k.value;else{const s=k.value.filter(_=>_.name.toLowerCase().includes(c.toLowerCase())||_.code.toLowerCase().includes(c.toLowerCase()));E.value[e]=s}}function ae(c){q.value[c]=!1,E.value[c]=[]}function te(c,e){if(!v.value[e])return;const s=v.value[e],_=s.expression||"",t=S.value[e]||_.length,l=_.substring(0,t),p=_.substring(t);let g=l;const m=l.lastIndexOf("@");if(m!==-1){const o=l.substring(m+1);(o.trim()===""||/^[\s]*$/.test(o))&&(g=l.substring(0,m))}const U=`{${c.name}}`,f=g+U+p;s.expression=f,ae(e)}function _e(c){return!c||c.trim()===""?"请输入表达式，支持中文、数字、运算符（+ - * /），输入@可选择变量":""}function ve(c){return c?c.replace(/\{([^}]+)\}/g,'<span class="variable-highlight">{$1}</span>'):""}function ue(){for(let c=0;c<v.value.length;c++)if(!v.value[c].title.trim())return P.error(`第${c+1}个字段的标题不能为空`),!1;return!0}async function me(){if(C.value){R.value=!0;try{if(!await new Promise(s=>{C.value.validate(_=>{s(_)})})||!ue())return;if(v.value.length===0){P.error("请至少保留一个字段");return}const e={id:$.planData.id,plan_name:y.value.plan_name,plan_config:v.value,...x.value==="asset"&&{asset_package:y.value.asset_package},...x.value==="mediationCases"&&{mediation_case:y.value.mediation_case}};N("ensure",e)}finally{R.value=!1}}}return(c,e)=>{const s=Ee,_=Pe,t=Se,l=Ae,p=Ue,g=Oe,m=Te,U=Re;return r(),A(xe,{visible:c.showDialog,"onUpdate:visible":K,width:"1200px",title:F.value},{default:u(()=>[a("div",Ga,[n(p,{ref_key:"planFormRef",ref:C,model:y.value,rules:G.value,"label-width":"110px"},{default:u(()=>[x.value==="asset"?(r(),A(t,{key:0,label:"资产包",prop:"asset_package"},{default:u(()=>[n(_,{modelValue:y.value.asset_package,"onUpdate:modelValue":e[0]||(e[0]=f=>y.value.asset_package=f),filterable:"",disabled:"",style:{width:"100%"}},{default:u(()=>[(r(!0),b(j,null,z(c.assetPackageOptions,f=>(r(),A(s,{key:f.value,label:f.label,value:f.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(r(),A(t,{key:1,label:"调解案件",prop:"mediation_case"},{default:u(()=>[n(_,{modelValue:y.value.mediation_case,"onUpdate:modelValue":e[1]||(e[1]=f=>y.value.mediation_case=f),filterable:"",disabled:"",style:{width:"100%"}},{default:u(()=>[(r(!0),b(j,null,z(c.mediationCaseOptions,f=>(r(),A(s,{key:f.value,label:f.label,value:f.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),n(t,{label:"方案名称",prop:"plan_name"},{default:u(()=>[n(l,{modelValue:y.value.plan_name,"onUpdate:modelValue":e[2]||(e[2]=f=>y.value.plan_name=f),placeholder:"例如方案一、方案二",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",Ja,[a("div",Ia,[Za,n(Q,{onClick:ne,height:34,"btn-type":"blue"},{default:u(()=>[Qa,V("添加字段 ")]),_:1})]),v.value.length>0?(r(),b("div",Wa,[(r(!0),b(j,null,z(v.value,(f,o)=>(r(),b("div",{key:f.id||`field-${o}`,class:"field-item"},[a("div",Ya,[a("span",et,"字段 "+O(o+1),1),n(Q,{onClick:d=>re(o),height:28,"btn-type":"red"},{default:u(()=>[at,V("删除 ")]),_:2},1032,["onClick"])]),a("div",tt,[a("div",st,[lt,n(_,{modelValue:f.type,"onUpdate:modelValue":d=>f.type=d,style:{width:"280px"}},{default:u(()=>[(r(),b(j,null,z(B,d=>n(s,{key:d.value,label:d.label,value:d.value},{default:u(()=>[a("span",ot,[a("i",{class:ge(d.icon),style:{"margin-right":"8px"}},null,2),V(" "+O(d.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),a("div",nt,[it,n(l,{modelValue:f.title,"onUpdate:modelValue":d=>f.title=d,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])])]),(r(),b("div",{class:"logic-type-section",key:`logic-type-${f.id}-${o}`},[ct,(r(),A(m,{modelValue:f.logic_type,"onUpdate:modelValue":d=>f.logic_type=d,class:"logic-type-radio-group",onChange:d=>de(d,o),key:`radio-group-${f.id}-${o}`},{default:u(()=>[(r(),A(g,{label:"text_format",size:"small",key:`text-format-${f.id}-${o}`},{default:u(()=>[V(" 文本格式化 ")]),_:2},1024)),(r(),A(g,{label:"result_calculation",size:"small",key:`result-calculation-${f.id}-${o}`},{default:u(()=>[V(" 结果运算 ")]),_:2},1024))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]))])),a("div",ut,[rt,a("div",dt,[a("div",pt,[n(l,{modelValue:f.expression,"onUpdate:modelValue":d=>f.expression=d,onInput:d=>ie(d,o),type:"textarea",rows:3,placeholder:_e(f.expression),onBlur:d=>pe(d,o),onKeydown:d=>ee(d,o),class:"expression-textarea"},null,8,["modelValue","onUpdate:modelValue","onInput","placeholder","onBlur","onKeydown"]),q.value[o]?(r(),b("div",_t,[a("div",vt,[mt,n(U,{size:"small",text:"",onClick:d=>ae(o)},{default:u(()=>[V("×")]),_:2},1032,["onClick"])]),a("div",gt,[n(l,{modelValue:T.value[o],"onUpdate:modelValue":d=>T.value[o]=d,size:"small",placeholder:"搜索变量名称...",onInput:d=>Z(T.value[o]||"",o),clearable:""},null,8,["modelValue","onUpdate:modelValue","onInput"])]),a("div",ft,[(r(!0),b(j,null,z(E.value[o]||k.value,d=>(r(),b("div",{key:d.code,onClick:ye(w=>te(d,o),["stop"]),onMousedown:e[3]||(e[3]=ye(()=>{},["prevent"])),class:"variable-item"},[a("span",bt,O(d.name),1)],40,ht))),128)),(E.value[o]||k.value).length===0?(r(),b("div",yt," 未找到匹配的变量 ")):Y("",!0)])])):Y("",!0)])])]),f.expression&&f.expression.trim()?(r(),b("div",kt,[wt,a("span",{class:"preview-content",innerHTML:ve(f.expression)},null,8,$t)])):Y("",!0),a("div",Vt,[V(" 内容预览："),a("div",Ct,O(f.preview),1)])]))),128))])):(r(),b("div",Dt,Pt))]),a("div",St,[n(Q,{onClick:me,loading:R.value,height:34,"btn-type":"blue"},{default:u(()=>[At,V("确认 ")]),_:1},8,["loading"]),n(Q,{onClick:K,height:34},{default:u(()=>[Ut,V("取消 ")]),_:1})])])]),_:1},8,["visible","title"])}}});const Tt=Ce(Ot,[["__scopeId","data-v-90d6a967"]]),De=D=>($e("data-v-45740b8e"),D=D(),Ve(),D),xt={class:"approval-content"},Ft={class:"plan-info"},Mt={class:"info-row"},Lt=De(()=>a("span",{class:"info-label"},"调解案件号",-1)),Nt={class:"info-value"},jt={class:"info-row"},Rt=De(()=>a("span",{class:"info-label"},"方案名称",-1)),Bt={class:"info-value"},zt={class:"btns-group"},qt=De(()=>a("i",{class:"jt-20-ensure"},null,-1)),Kt=De(()=>a("i",{class:"jt-20-delete"},null,-1)),Xt=we({__name:"ApprovalDialog",props:{showDialog:{type:Boolean},planData:{}},emits:["close","ensure"],setup(D,{emit:N}){const $=D,C=h(),R=h(!1),y=h({approval_status:"",approval_comment:""}),v={approval_status:[{required:!0,message:"请选择审批结果",trigger:"change"}]};se(()=>$.showDialog,E=>{E&&k()});function k(){y.value={approval_status:"",approval_comment:""},C.value&&C.value.clearValidate()}function M(){N("close")}async function q(){var E;if(C.value){R.value=!0;try{if(!await new Promise(B=>{C.value.validate(x=>{B(x)})}))return;if(!((E=$.planData)!=null&&E.id)){P.error("未找到方案信息");return}const T={id:Number($.planData.id),approval_status:y.value.approval_status,approval_comment:y.value.approval_comment||void 0};N("ensure",T)}finally{R.value=!1}}}return(E,S)=>{const T=Oe,B=Te,x=Se,F=Ae,G=Ue;return r(),A(xe,{visible:E.showDialog,"onUpdate:visible":M,width:"600px",title:"审批方案"},{default:u(()=>{var J,I;return[a("div",xt,[a("div",Ft,[a("div",Mt,[Lt,a("span",Nt,O((J=E.planData)==null?void 0:J.mediation_case_number),1)]),a("div",jt,[Rt,a("span",Bt,O((I=E.planData)==null?void 0:I.plan_name),1)])]),n(G,{ref_key:"approvalFormRef",ref:C,model:y.value,rules:v,"label-width":"100px","label-position":"left"},{default:u(()=>[n(x,{label:"审批结果",prop:"approval_status"},{default:u(()=>[n(B,{modelValue:y.value.approval_status,"onUpdate:modelValue":S[0]||(S[0]=K=>y.value.approval_status=K)},{default:u(()=>[n(T,{label:"approved"},{default:u(()=>[V("通过")]),_:1}),n(T,{label:"rejected"},{default:u(()=>[V("不通过")]),_:1})]),_:1},8,["modelValue"])]),_:1}),n(x,{label:"审批意见",prop:"approval_comment"},{default:u(()=>[n(F,{modelValue:y.value.approval_comment,"onUpdate:modelValue":S[1]||(S[1]=K=>y.value.approval_comment=K),type:"textarea",rows:4,placeholder:"请输入审批意见",maxlength:"500","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),a("div",zt,[n(Q,{onClick:q,loading:R.value,height:34,"btn-type":"blue"},{default:u(()=>[qt,V("确认 ")]),_:1},8,["loading"]),n(Q,{onClick:M,height:34},{default:u(()=>[Kt,V("取消 ")]),_:1})])])]}),_:1},8,["visible"])}}});const Ht=Ce(Xt,[["__scopeId","data-v-45740b8e"]]),Fe=D=>($e("data-v-a471e577"),D=D(),Ve(),D),Gt={class:"plan-management"},Jt={class:"search-header"},It={class:"search-row"},Zt={class:"search-item"},Qt={class:"search-item"},Wt=Fe(()=>a("label",null,"审批状态",-1)),Yt={class:"search-item"},es=Fe(()=>a("label",null,"方案状态",-1)),as={class:"search-item"},ts=Fe(()=>a("i",{class:"jt-20-add"},null,-1)),ss={class:"fields-preview"},ls={class:"field-types"},os=["title"],ns={key:0,class:"more-fields"},is={class:"operation-buttons"},cs=["onClick"],us=["onClick"],rs={class:"fields-preview"},ds={class:"field-types"},ps=["title"],_s={key:0,class:"more-fields"},vs={class:"operation-buttons"},ms=["onClick"],gs=["onClick"],fs=["onClick"],hs={class:"pagination-wrapper"},be=10,bs=we({__name:"disposalPlan",setup(D){const N=h("asset"),$=h(!1),C=h({id:"",title:"",fields:[],jsonData:"",caseStatus:"",approvalStatus:"",plan_status_cn:""}),R=h(0),y=h(1),v=h(""),k=h(""),M=h(""),q=h([]),E=h([]),S=h(!1),T=h(!1),B=h([]),x=h(!1),F=h(!1),G=h(!1),J=h(!1),I=h([]),K=h([]),W=h([]),ne=qe(),re=oe(()=>{const t=sessionStorage.getItem("group_name")||"",l=sessionStorage.getItem("role_name")||"",p=["审批部","调解中心","管理层","主管部门"],g=["审批员","审批主管","调解员","部门经理","总监"],m=p.some(o=>t.includes(o)),U=g.some(o=>l.includes(o)),f=ne.hasPermission("mediation_plan_approve")||ne.hasPermission("审批调解方案");return m||U||f});async function de(){S.value=!0;const{data:t}=await Xe(),{state:l,msg:p}=t;if(l==="success"){const{approval_status_choices:g,plan_status_choices:m}=t.data;t.data&&(q.value=g,E.value=m)}else P.error(p);S.value=!1}async function ie(){const{data:t}=await He({page:1,page_size:1e3,package_status:"available"}),{state:l,msg:p}=t;l==="success"?(I.value=t.data.results.map(g=>({label:g.package_name,value:g.id})),W.value=t.data.results):P.error(p)}async function ce(){const{data:t}=await je({page:1,page_size:1e3,package_status:"available"}),{state:l,msg:p}=t;l==="success"?(K.value=t.data.results.map(g=>({label:g.case_number,value:g.id})),W.value=t.data.results):P.error(p||"获取数据失败")}function pe(t){N.value=t,y.value=1,t==="asset"?($.value=!1,ie()):($.value=!0,ce()),Z()}function ee(){y.value=1,Z()}async function Z(){T.value=!0;const t={page:y.value,page_size:be,search:v.value,asset_package__isnull:$.value,plan_status:M.value,approval_status:k.value},{data:l}=await Ge(t),{state:p,msg:g}=l;if(p==="success"){const{results:m,count:U}=l.data;B.value=m,R.value=U}else P.error(g);T.value=!1}function ae(t){y.value=t,Z()}function te(){x.value=!0}async function _e(t){const l={plan_name:t.plan_name,plan_config:t.plan_config};t.asset_package&&(l.asset_package=t.asset_package),t.mediation_case&&(l.mediation_case=t.mediation_case);const{data:p}=await Je(l),{state:g,msg:m}=p;g==="success"?(P.success("新增成功"),x.value=!1,ee()):P.error(m||"新增失败")}function ve(t,l){C.value={...t},J.value=!0}async function ue(t,l){const{data:p}=await ea(Number(t.id)),{state:g,msg:m}=p;g==="success"?C.value={...p.data}:P.error(m),F.value=!0}async function me(t){const{data:l}=await Ie(t,Number(t.id)),{state:p,msg:g}=l;p==="success"?(P.success(g),F.value=!1,Z()):P.error(g||"编辑失败")}async function c(t){console.log(t.approval_status,"审批方案参数:",t);const l={approval_status:t.approval_status,approval_comment:t.approval_comment},{data:p}=await Ze(l,t.id),{state:g,msg:m}=p;g==="success"?(P.success(m||"审批成功"),J.value=!1,Z()):P.error(m||"审批失败")}function e(t,l){C.value={...t},G.value=!0}async function s(){try{console.log("删除调解案件，ID:",C.value.id);const{data:t}=await Qe(Number(C.value.id)),{state:l,msg:p}=t;l==="success"?(P.success("删除成功"),G.value=!1,Z()):P.error(p||"删除失败")}catch(t){console.error("删除调解案件失败:",t),P.error("删除失败")}}function _(){F.value=!1,C.value={asset_package:"",title:"",fields:[],jsonData:"",caseStatus:"",approvalStatus:"",schemeStatus:""}}return Ke(()=>{Z(),de(),N.value==="asset"?ie():ce()}),(t,l)=>{const p=Ee,g=Pe,m=aa,U=ta,f=sa,o=We,d=Ye,w=la;return r(),b(j,null,[a("div",Gt,[a("div",Jt,[a("div",It,[a("div",Zt,[n(Be,{modelValue:v.value,"onUpdate:modelValue":l[0]||(l[0]=i=>v.value=i),placeholder:"搜索资产包名称或调解案件号",onClick:ee},null,8,["modelValue"])]),a("div",Qt,[Wt,n(g,{modelValue:k.value,"onUpdate:modelValue":l[1]||(l[1]=i=>k.value=i),placeholder:"审批状态",clearable:"",onChange:ee,loading:S.value,style:{width:"200px"}},{default:u(()=>[(r(!0),b(j,null,z(q.value,i=>(r(),A(p,{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),a("div",Yt,[es,n(g,{modelValue:M.value,"onUpdate:modelValue":l[2]||(l[2]=i=>M.value=i),placeholder:"方案状态",clearable:"",onChange:ee,loading:S.value,style:{width:"200px"}},{default:u(()=>[(r(!0),b(j,null,z(E.value,i=>(r(),A(p,{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),a("div",as,[n(Q,{onClick:te,height:34},{default:u(()=>[ts,V("新增方案")]),_:1})])])]),n(o,{modelValue:N.value,"onUpdate:modelValue":l[3]||(l[3]=i=>N.value=i),onTabChange:pe,class:"plan-tabs"},{default:u(()=>[n(f,{label:"资产包",name:"asset",class:"asset-tab"},{default:u(()=>[a("div",null,[Ne((r(),A(U,{data:B.value,border:"","cell-style":he(Me),"header-cell-style":he(Le),class:"plan-table"},{default:u(()=>[n(m,{type:"index",label:"序号",width:"60",align:"center"},{default:u(({$index:i})=>[V(O(be*(y.value-1)+i+1),1)]),_:1}),n(m,{prop:"asset_package_name",label:"资产包名称",align:"center","min-width":"210"}),n(m,{prop:"plan_name",label:"方案名称",align:"center","min-width":"210"}),n(m,{label:"方案配置",align:"center","min-width":"210"},{default:u(({row:i})=>[a("div",ss,[a("div",ls,[(r(!0),b(j,null,z(i.plan_config&&Array.isArray(i.plan_config)?i.plan_config.slice(0,4):[],L=>(r(),b("span",{key:L.id||L.title||Math.random(),class:"field-type-tag",title:L.title},O(L.title),9,os))),128)),i.plan_config&&Array.isArray(i.plan_config)&&i.plan_config.length>4?(r(),b("span",ns," +"+O(i.plan_config.length-4),1)):Y("",!0)])])]),_:1}),n(m,{prop:"plan_status_cn",label:"方案状态",align:"center","min-width":"100"},{default:u(({row:i})=>[a("span",{class:ge({"text-success":i.plan_status_cn==="已生效","text-warning":i.plan_status_cn=="未生效"})},O(i.plan_status_cn),3)]),_:1}),n(m,{label:"操作",align:"center",width:"250"},{default:u(({row:i,$index:L})=>[a("div",is,[a("div",{onClick:fe=>ue(i,L),class:"operation-btn edit-btn"},"编辑",8,cs),a("div",{onClick:fe=>e(i,L),class:"operation-btn delete-btn"},"删除",8,us)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[w,T.value]])])]),_:1}),n(f,{label:"调解案件",name:"mediationCases",class:"mediation-cases-tab"},{default:u(()=>[a("div",null,[Ne((r(),A(U,{data:B.value,border:"","cell-style":he(Me),"header-cell-style":he(Le),class:"plan-table"},{default:u(()=>[n(m,{type:"index",label:"序号",width:"60",align:"center"},{default:u(({$index:i})=>[V(O(be*(y.value-1)+i+1),1)]),_:1}),n(m,{prop:"mediation_case_number",label:"调解案件号",align:"center","min-width":"210"}),n(m,{prop:"plan_name",label:"方案名称",align:"center","min-width":"210"}),n(m,{label:"方案配置",align:"center","min-width":"210"},{default:u(({row:i})=>[a("div",rs,[a("div",ds,[(r(!0),b(j,null,z(i.plan_config&&Array.isArray(i.plan_config)?i.plan_config.slice(0,4):[],L=>(r(),b("span",{key:L.id||L.title||Math.random(),class:"field-type-tag",title:L.title},O(L.title),9,ps))),128)),i.plan_config&&Array.isArray(i.plan_config)&&i.plan_config.length>4?(r(),b("span",_s," +"+O(i.plan_config.length-4),1)):Y("",!0)])])]),_:1}),n(m,{prop:"approval_status_cn",label:"审批状态",align:"center","min-width":"100"},{default:u(({row:i})=>[a("span",{class:ge({"text-success":i.approval_status_cn==="已通过","text-danger":i.approval_status_cn=="待审批","text-warning":i.approval_status_cn=="未通过"})},O(i.approval_status_cn),3)]),_:1}),n(m,{prop:"plan_status_cn",label:"方案状态",align:"center","min-width":"100"},{default:u(({row:i})=>[a("span",{class:ge({"text-success":i.plan_status_cn==="已生效","text-warning":i.plan_status_cn=="未生效"})},O(i.plan_status_cn),3)]),_:1}),n(m,{label:"操作",align:"center",width:"250"},{default:u(({row:i,$index:L})=>[a("div",vs,[re.value&&i.approval_status_cn==="待审批"?(r(),b("div",{key:0,onClick:fe=>ve(i,L),class:"operation-btn edit-btn"},"审批",8,ms)):Y("",!0),a("div",{onClick:fe=>ue(i,L),class:"operation-btn edit-btn"},"编辑",8,gs),a("div",{onClick:fe=>e(i,L),class:"operation-btn delete-btn"},"删除",8,fs)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[w,T.value]])])]),_:1})]),_:1},8,["modelValue"]),a("div",hs,[n(d,{background:"",layout:"prev, pager, next",total:R.value,"current-page":y.value,"page-size":be,onCurrentChange:ae},null,8,["total","current-page"])])]),n(Ha,{"show-dialog":x.value,"current-mode":N.value,mediationCaseOptions:K.value,assetPackageOptions:I.value,assetPackageData:W.value,onClose:l[4]||(l[4]=i=>x.value=!1),onEnsure:_e},null,8,["show-dialog","current-mode","mediationCaseOptions","assetPackageOptions","assetPackageData"]),n(Tt,{"show-dialog":F.value,"plan-data":C.value,"current-mode":N.value,mediationCaseOptions:K.value,assetPackageOptions:I.value,onClose:_,onEnsure:me},null,8,["show-dialog","plan-data","current-mode","mediationCaseOptions","assetPackageOptions"]),n(oa,{visible:G.value,title:"删除调解方案",message:"确认删除选中的调解方案吗？此操作不可撤销。","confirm-text":"确认","cancel-text":"取消","onUpdate:visible":l[5]||(l[5]=i=>G.value=i),onConfirm:s,onCancel:l[6]||(l[6]=i=>G.value=!1)},null,8,["visible"]),n(Ht,{"show-dialog":J.value,"plan-data":C.value,onClose:l[7]||(l[7]=i=>J.value=!1),onEnsure:c},null,8,["show-dialog","plan-data"])],64)}}});const Fs=Ce(bs,[["__scopeId","data-v-a471e577"]]);export{Fs as default};

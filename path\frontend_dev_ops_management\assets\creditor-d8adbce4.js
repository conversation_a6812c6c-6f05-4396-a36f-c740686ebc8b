/* empty css             */import{C as Ve,c as Ce,h as ke}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as ae,r as p,v as me,o as d,q as x,w as i,K as te,e as V,g as t,F as A,B as O,f as u,h as b,G as F,E,X as se,U as ie,k as _e,j as ve,a2 as fe,a4 as ye,l as he,N as oe,p as ne,m as re,ai as ue,A as we,J as $e,n as pe,aj as Ue,ak as xe,al as Ee,am as Ae,an as Oe,L as Pe,M as Te,t as ee,ao as ze,ap as je,O as Ie}from"./index-d4ffb1a1.js";import{C as W}from"./CustomButton-777ba9d4.js";/* empty css                     *//* empty css                  *//* empty css                 */import{_ as ge}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";import{D as Se}from"./DeleteConfirmDialog-5eb37e5a.js";/* empty css                                                                            */const Y=C=>(ne("data-v-ed19b7f3"),C=C(),re(),C),Be={class:"add-creditor-form"},qe={class:"contact-list"},Le={class:"contact-input-group"},Fe=Y(()=>u("i",{class:"el-icon-plus"},null,-1)),Ne={class:"contact-list"},Re={class:"contact-input-group"},De=Y(()=>u("i",{class:"el-icon-plus"},null,-1)),Ke={class:"contact-list"},Me={class:"contact-input-group"},Ge={class:"contact-controls"},Je=Y(()=>u("i",{class:"el-icon-plus"},null,-1)),Xe={class:"dialog-footer"},He=Y(()=>u("i",{class:"jt-20-ensure"},null,-1)),Qe=Y(()=>u("i",{class:"jt-20-delete"},null,-1)),We=ae({__name:"AddCreditor",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(C,{emit:I}){const S=C,w=p(),k=p(!1),g=p({creditor_type:"",creditor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),_=p([{phone:"",phone_type:"",is_primary:!0}]),v=p([{email:"",email_type:"",is_primary:!0}]),f=p([{address:"",address_type:"",is_primary:!0}]),P=p([]),T=p([]),$=p(!1);async function B(){$.value=!0;const{data:e}=await ue(),{state:s,msg:m}=e;if(s==="success"){const{creditor_types:r,id_types:o}=e.data;if(e.data){const n=Object.entries(r).map(([c,U])=>({label:U,value:c}));P.value=n;const y=Object.entries(o).map(([c,U])=>({label:U,value:c}));T.value=y}}else E.error(m);$.value=!1}const N={creditor_type:[{required:!0,message:"请选择类型",trigger:"change"}],creditor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};me(()=>S.visible,async e=>{e&&(await B(),R())});function R(){g.value={creditor_name:"",creditor_type:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]},_.value=[{phone:"",phone_type:"",is_primary:!0}],v.value=[{email:"",email_type:"",is_primary:!0}],f.value=[{address:"",address_type:"",is_primary:!0}],w.value&&w.value.clearValidate()}function z(){I("update:visible")}function D(){_.value.push({phone:"",phone_type:"",is_primary:!1})}function j(e){_.value.length>1&&(_.value.splice(e,1),!_.value.find(s=>s.is_primary)&&_.value.length>0&&(_.value[0].is_primary=!0))}function q(e){_.value.forEach((s,m)=>{s.is_primary=m===e})}function K(){v.value.push({email:"",email_type:"",is_primary:!1})}function M(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(s=>s.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function G(e){v.value.forEach((s,m)=>{s.is_primary=m===e})}function J(){f.value.push({address:"",address_type:"",is_primary:!1})}function X(e){f.value.length>1&&(f.value.splice(e,1),!f.value.find(s=>s.is_primary)&&f.value.length>0&&(f.value[0].is_primary=!0))}function H(e){f.value.forEach((s,m)=>{s.is_primary=m===e})}async function Q(){if(w.value){k.value=!0;try{if(!await new Promise(n=>{w.value.validate(y=>{n(y)})}))return;const s=_.value.filter(n=>n.phone.trim()),m=v.value.filter(n=>n.email.trim()),r=f.value.filter(n=>n.address.trim()),o={...g.value,phones:s.length>0?s:void 0,emails:m.length>0?m:void 0,addresses:r.length>0?r:void 0};I("confirm",o)}catch(e){console.error("表单验证失败:",e),E.error(e)}finally{k.value=!1}}}return(e,s)=>{const m=se,r=ie,o=_e,n=ve,y=fe,c=ye,U=he,L=oe;return d(),x(ge,{visible:S.visible,title:"新增债权人",width:"600px","onUpdate:visible":z},{default:i(()=>[te((d(),V("div",Be,[t(U,{ref_key:"formRef",ref:w,model:g.value,rules:N,"label-width":"120px","label-position":"right"},{default:i(()=>[t(o,{label:"类型",prop:"creditor_type"},{default:i(()=>[t(r,{modelValue:g.value.creditor_type,"onUpdate:modelValue":s[0]||(s[0]=l=>g.value.creditor_type=l),placeholder:"请选择类型",loading:$.value,style:{width:"100%"}},{default:i(()=>[(d(!0),V(A,null,O(P.value,l=>(d(),x(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),t(o,{label:"姓名",prop:"creditor_name"},{default:i(()=>[t(n,{modelValue:g.value.creditor_name,"onUpdate:modelValue":s[1]||(s[1]=l=>g.value.creditor_name=l),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),t(o,{label:"证件类型",prop:"id_type"},{default:i(()=>[t(r,{modelValue:g.value.id_type,"onUpdate:modelValue":s[2]||(s[2]=l=>g.value.id_type=l),placeholder:"请选择证件类型",loading:$.value,style:{width:"100%"}},{default:i(()=>[(d(!0),V(A,null,O(T.value,l=>(d(),x(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),t(o,{label:"证件号码",prop:"id_number"},{default:i(()=>[t(n,{modelValue:g.value.id_number,"onUpdate:modelValue":s[3]||(s[3]=l=>g.value.id_number=l),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),t(o,{label:"联系电话"},{default:i(()=>[u("div",qe,[(d(!0),V(A,null,O(_.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",Le,[t(n,{modelValue:l.phone,"onUpdate:modelValue":a=>l.phone=a,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>q(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),_.value.length>1?(d(),x(c,{key:0,onClick:a=>j(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])]))),128)),t(c,{onClick:D,type:"primary",text:"",size:"small"},{default:i(()=>[Fe,b(" 添加电话 ")]),_:1})])]),_:1}),t(o,{label:"联系邮箱"},{default:i(()=>[u("div",Ne,[(d(!0),V(A,null,O(v.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",Re,[t(n,{modelValue:l.email,"onUpdate:modelValue":a=>l.email=a,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>G(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(d(),x(c,{key:0,onClick:a=>M(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])]))),128)),t(c,{onClick:K,type:"primary",text:"",size:"small"},{default:i(()=>[De,b(" 添加邮箱 ")]),_:1})])]),_:1}),t(o,{label:"联系地址"},{default:i(()=>[u("div",Ke,[(d(!0),V(A,null,O(f.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",Me,[t(n,{modelValue:l.address,"onUpdate:modelValue":a=>l.address=a,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),u("div",Ge,[t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>H(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),f.value.length>1?(d(),x(c,{key:0,onClick:a=>X(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])])]))),128)),t(c,{onClick:J,type:"primary",text:"",size:"small"},{default:i(()=>[Je,b(" 添加地址 ")]),_:1})])]),_:1})]),_:1},8,["model"]),u("div",Xe,[t(W,{onClick:Q,loading:k.value,height:34,"btn-type":"blue"},{default:i(()=>[He,b("确认 ")]),_:1},8,["loading"]),t(W,{onClick:z,height:34},{default:i(()=>[Qe,b("取消 ")]),_:1})])])),[[L,k.value]])]),_:1},8,["visible"])}}});const Ye=de(We,[["__scopeId","data-v-ed19b7f3"]]),Z=C=>(ne("data-v-5a7a582a"),C=C(),re(),C),Ze={class:"edit-creditor-form"},el={class:"contact-list"},ll={class:"contact-input-group"},al=Z(()=>u("i",{class:"el-icon-plus"},null,-1)),tl={class:"contact-list"},sl={class:"contact-input-group"},il=Z(()=>u("i",{class:"el-icon-plus"},null,-1)),ol={class:"contact-list"},nl={class:"contact-input-group"},rl={class:"contact-controls"},ul=Z(()=>u("i",{class:"el-icon-plus"},null,-1)),dl={class:"dialog-footer"},cl=Z(()=>u("i",{class:"jt-20-ensure"},null,-1)),pl=Z(()=>u("i",{class:"jt-20-delete"},null,-1)),ml=ae({__name:"EditCreditor",props:{visible:{type:Boolean,default:!1},creditorData:{default:null}},emits:["update:visible","confirm"],setup(C,{emit:I}){const S=C,w=p(),k=p(!1),g=p({id:0,creditor_type:"",creditor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),_=p([{phone:"",phone_type:"",is_primary:!0}]),v=p([{email:"",email_type:"",is_primary:!0}]),f=p([{address:"",address_type:"",is_primary:!0}]),P=p([]),T=p([]),$=p(!1);async function B(){$.value=!0;const{data:e}=await ue(),{state:s,msg:m}=e;if(s==="success"){const{creditor_types:r,id_types:o}=e.data;if(e.data){const n=Object.entries(r).map(([c,U])=>({label:U,value:c}));P.value=n;const y=Object.entries(o).map(([c,U])=>({label:U,value:c}));T.value=y}}else E.error(m);$.value=!1}const N={creditor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],creditor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};me(()=>[S.visible,S.creditorData],async([e,s])=>{e&&s&&typeof s=="object"&&(await B(),R(s))},{immediate:!0});function R(e){g.value={id:e.id,creditor_type:e.creditor_type,creditor_name:e.creditor_name,id_type:e.id_type,id_number:e.id_number,phones:e.phones||[],emails:e.emails||[],addresses:e.addresses||[]},e.phones&&e.phones.length>0?_.value=[...e.phones]:e.phone?_.value=[{phone:e.phone,phone_type:"",is_primary:!0}]:_.value=[{phone:"",phone_type:"",is_primary:!0}],e.emails&&e.emails.length>0?v.value=[...e.emails]:e.email?v.value=[{email:e.email,email_type:"",is_primary:!0}]:v.value=[{email:"",email_type:"",is_primary:!0}],e.addresses&&e.addresses.length>0?f.value=[...e.addresses]:e.address?f.value=[{address:e.address,address_type:"",is_primary:!0}]:f.value=[{address:"",address_type:"",is_primary:!0}],w.value&&w.value.clearValidate()}function z(){I("update:visible")}function D(){_.value.push({phone:"",phone_type:"",is_primary:!1})}function j(e){_.value.length>1&&(_.value.splice(e,1),!_.value.find(s=>s.is_primary)&&_.value.length>0&&(_.value[0].is_primary=!0))}function q(e){_.value.forEach((s,m)=>{s.is_primary=m===e})}function K(){v.value.push({email:"",email_type:"",is_primary:!1})}function M(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(s=>s.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function G(e){v.value.forEach((s,m)=>{s.is_primary=m===e})}function J(){f.value.push({address:"",address_type:"",is_primary:!1})}function X(e){f.value.length>1&&(f.value.splice(e,1),!f.value.find(s=>s.is_primary)&&f.value.length>0&&(f.value[0].is_primary=!0))}function H(e){f.value.forEach((s,m)=>{s.is_primary=m===e})}async function Q(){if(w.value){k.value=!0;try{if(!await new Promise(n=>{w.value.validate(y=>{n(y)})}))return;const s=_.value.filter(n=>n.phone.trim()),m=v.value.filter(n=>n.email.trim()),r=f.value.filter(n=>n.address.trim()),o={...g.value,phones:s.length>0?s:void 0,emails:m.length>0?m:void 0,addresses:r.length>0?r:void 0};I("confirm",o)}catch(e){console.error("表单验证失败:",e),E.error(e)}finally{k.value=!1}}}return(e,s)=>{const m=se,r=ie,o=_e,n=ve,y=fe,c=ye,U=he,L=oe;return d(),x(ge,{visible:S.visible,title:"编辑债权人",width:"600px","onUpdate:visible":z},{default:i(()=>[te((d(),V("div",Ze,[t(U,{ref_key:"formRef",ref:w,model:g.value,rules:N,"label-width":"120px","label-position":"right"},{default:i(()=>[t(o,{label:"类型",prop:"creditor_type"},{default:i(()=>[t(r,{modelValue:g.value.creditor_type,"onUpdate:modelValue":s[0]||(s[0]=l=>g.value.creditor_type=l),placeholder:"请选择债权人类型",loading:$.value,style:{width:"100%"}},{default:i(()=>[(d(!0),V(A,null,O(P.value,l=>(d(),x(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),t(o,{label:"姓名",prop:"creditor_name"},{default:i(()=>[t(n,{modelValue:g.value.creditor_name,"onUpdate:modelValue":s[1]||(s[1]=l=>g.value.creditor_name=l),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),t(o,{label:"证件类型",prop:"id_type"},{default:i(()=>[t(r,{modelValue:g.value.id_type,"onUpdate:modelValue":s[2]||(s[2]=l=>g.value.id_type=l),placeholder:"请选择证件类型",loading:$.value,style:{width:"100%"}},{default:i(()=>[(d(!0),V(A,null,O(T.value,l=>(d(),x(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),t(o,{label:"证件号码",prop:"id_number"},{default:i(()=>[t(n,{modelValue:g.value.id_number,"onUpdate:modelValue":s[3]||(s[3]=l=>g.value.id_number=l),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),t(o,{label:"联系电话"},{default:i(()=>[u("div",el,[(d(!0),V(A,null,O(_.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",ll,[t(n,{modelValue:l.phone,"onUpdate:modelValue":a=>l.phone=a,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>q(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),_.value.length>1?(d(),x(c,{key:0,onClick:a=>j(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])]))),128)),t(c,{onClick:D,type:"primary",text:"",size:"small"},{default:i(()=>[al,b(" 添加电话 ")]),_:1})])]),_:1}),t(o,{label:"联系邮箱"},{default:i(()=>[u("div",tl,[(d(!0),V(A,null,O(v.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",sl,[t(n,{modelValue:l.email,"onUpdate:modelValue":a=>l.email=a,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>G(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(d(),x(c,{key:0,onClick:a=>M(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])]))),128)),t(c,{onClick:K,type:"primary",text:"",size:"small"},{default:i(()=>[il,b(" 添加邮箱 ")]),_:1})])]),_:1}),t(o,{label:"联系地址"},{default:i(()=>[u("div",ol,[(d(!0),V(A,null,O(f.value,(l,h)=>(d(),V("div",{key:h,class:"contact-item"},[u("div",nl,[t(n,{modelValue:l.address,"onUpdate:modelValue":a=>l.address=a,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),u("div",rl,[t(y,{modelValue:l.is_primary,"onUpdate:modelValue":a=>l.is_primary=a,label:!0,onChange:a=>H(h),class:"primary-radio"},{default:i(()=>[b("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),f.value.length>1?(d(),x(c,{key:0,onClick:a=>X(h),type:"danger",text:"",size:"small"},{default:i(()=>[b("删除")]),_:2},1032,["onClick"])):F("",!0)])])]))),128)),t(c,{onClick:J,type:"primary",text:"",size:"small"},{default:i(()=>[ul,b(" 添加地址 ")]),_:1})])]),_:1})]),_:1},8,["model"]),u("div",dl,[t(W,{onClick:Q,loading:k.value,height:34,"btn-type":"blue"},{default:i(()=>[cl,b("确认 ")]),_:1},8,["loading"]),t(W,{onClick:z,height:34},{default:i(()=>[pl,b("取消 ")]),_:1})])])),[[L,k.value]])]),_:1},8,["visible"])}}});const _l=de(ml,[["__scopeId","data-v-5a7a582a"]]),ce=C=>(ne("data-v-87517745"),C=C(),re(),C),vl={class:"creditor-management-page"},fl={class:"search-header"},yl={class:"search-row"},hl={class:"search-item"},gl={class:"search-item"},bl=ce(()=>u("label",null,"债权人类型",-1)),Vl={class:"search-item"},Cl=ce(()=>u("label",null,"证件类型",-1)),kl={class:"search-item"},wl=ce(()=>u("i",{class:"jt-20-add"},null,-1)),$l={class:"table-container"},Ul={class:"operation-buttons"},xl=["onClick"],El=["onClick"],Al={key:0,class:"pagination-wrapper"},le=10,Ol=ae({__name:"creditor",setup(C){const I=p(!1),S=p([]),w=p(0),k=p(1),g=p(""),_=p(""),v=p(""),f=p(!1),P=p(!1),T=p(null),$=p(!1),B=p(null),N=p([]),R=p([]),z=p(!1);async function D(){z.value=!0;const{data:r}=await ue(),{state:o,msg:n}=r;if(o==="success"){const{creditor_types:y,id_types:c}=r.data;if(r.data){const U=Object.entries(y).map(([l,h])=>({label:h,value:l}));N.value=U;const L=Object.entries(c).map(([l,h])=>({label:h,value:l}));R.value=L}}else E.error(n);z.value=!1}async function j(){I.value=!0;const r={page:k.value,page_size:le};g.value.trim()&&(r.search=g.value.trim()),_.value&&(r.creditor_type=_.value),v.value&&(r.id_type=v.value);const{data:o}=await Ue(r),{state:n,msg:y}=o;n==="success"?(S.value=o.data.results,w.value=o.data.count):E.error(y),I.value=!1}function q(){xe("搜索","债权人管理"),k.value=1,j()}function K(r){k.value=r,j()}function M(){f.value=!0}function G(){f.value=!1}async function J(r){const{data:o}=await Ee(r),{state:n,msg:y}=o;n==="success"&&(E.success(y),f.value=!1,k.value=1,j())}async function X(r){const{data:o}=await ze(r.id),{state:n,msg:y}=o;n==="success"?(T.value=o.data,P.value=!0):E.error(y)}function H(){P.value=!1,T.value=null}async function Q(r){const{data:o}=await Ae(r,r.id),{state:n,msg:y}=o;n==="success"&&(E.success(y),P.value=!1,T.value=null,j())}function e(r){if(!je()){E.error("登录状态已失效，请重新登录");return}B.value=r,$.value=!0}async function s(){if(B.value)try{await Oe(B.value.id),E.success("删除成功"),j()}catch(r){E.error(r)}finally{$.value=!1,B.value=null}}function m(r,o){if(!r||r.length===0)return"";const n=r.find(y=>y.is_primary);return n?n[o]||"":r[0][o]}return we(async()=>{await D(),await j()}),(r,o)=>{var h;const n=se,y=ie,c=Ie,U=Pe,L=Te,l=oe;return d(),V("div",vl,[u("div",fl,[u("div",yl,[u("div",hl,[t(Ve,{modelValue:g.value,"onUpdate:modelValue":o[0]||(o[0]=a=>g.value=a),placeholder:"搜索姓名、证件号码",onKeyup:$e(q,["enter"]),onClick:q},null,8,["modelValue","onKeyup"])]),u("div",gl,[bl,t(y,{modelValue:_.value,"onUpdate:modelValue":o[1]||(o[1]=a=>_.value=a),placeholder:"债权人类型",clearable:"",onChange:q,loading:z.value,style:{width:"200px"}},{default:i(()=>[(d(!0),V(A,null,O(N.value,a=>(d(),x(n,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),u("div",Vl,[Cl,t(y,{modelValue:v.value,"onUpdate:modelValue":o[2]||(o[2]=a=>v.value=a),placeholder:"证件类型",clearable:"",onChange:q,loading:z.value,style:{width:"140px"}},{default:i(()=>[(d(!0),V(A,null,O(R.value,a=>(d(),x(n,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),u("div",kl,[t(W,{onClick:M,height:34},{default:i(()=>[wl,b("新增债权人 ")]),_:1})])])]),u("div",$l,[te((d(),x(U,{data:S.value,border:"",style:{width:"100%"},"cell-style":pe(Ce),"header-cell-style":pe(ke)},{default:i(()=>[t(c,{type:"index",label:"序号",width:"80",align:"center"},{default:i(({$index:a})=>[b(ee(le*(k.value-1)+a+1),1)]),_:1}),t(c,{label:"类型",width:"140",align:"center",prop:"creditor_type_cn"}),t(c,{align:"center",prop:"creditor_name",label:"姓名","min-width":"200"}),t(c,{label:"证件类型",width:"120",align:"center",prop:"id_type_cn"}),t(c,{align:"center",prop:"id_number",label:"证件号码","min-width":"200"}),t(c,{align:"center",label:"联系电话",width:"130"},{default:i(({row:a})=>[b(ee(m(a.phones,"phone")),1)]),_:1}),t(c,{align:"center",label:"联系邮箱",width:"180"},{default:i(({row:a})=>[b(ee(m(a.emails,"email")),1)]),_:1}),t(c,{align:"center",label:"联系地址","min-width":"200"},{default:i(({row:a})=>[b(ee(m(a.addresses,"address")),1)]),_:1}),t(c,{label:"操作",width:"180",align:"center",fixed:"right"},{default:i(({row:a})=>[u("div",Ul,[u("div",{onClick:be=>X(a),class:"operation-btn edit-btn"},"编辑",8,xl),u("div",{onClick:be=>e(a),class:"operation-btn delete-btn"},"删除",8,El)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[l,I.value]]),w.value>0?(d(),V("div",Al,[t(L,{background:"",layout:"prev, pager, next",total:w.value,"current-page":k.value,"page-size":le,onCurrentChange:K},null,8,["total","current-page"])])):F("",!0)]),t(Ye,{visible:f.value,"onUpdate:visible":G,onConfirm:J},null,8,["visible"]),t(_l,{visible:P.value,"creditor-data":T.value,"onUpdate:visible":H,onConfirm:Q},null,8,["visible","creditor-data"]),t(Se,{visible:$.value,title:"删除债权人",message:`确定要删除债权人「${(h=B.value)==null?void 0:h.creditor_name}」吗？此操作不可撤销。`,"confirm-text":"确认","cancel-text":"取消","onUpdate:visible":o[3]||(o[3]=a=>$.value=a),onConfirm:s,onCancel:o[4]||(o[4]=a=>$.value=!1)},null,8,["visible","message"])])}}});const Kl=de(Ol,[["__scopeId","data-v-87517745"]]);export{Kl as default};

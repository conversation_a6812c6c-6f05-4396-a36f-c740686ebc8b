<template>
  <CustomDialog
    :title="'预览调解方案内容'"
    :visible="visible"
    width="900px"
    :markclose="true"
    @update:visible="handleClose">
    
    <div class="preview-content">
      <div class="info-section" v-if="rowData">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">调解案件号：</span>
            <span class="value">{{ rowData.case_number || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债权人：</span>
            <span class="value">{{ rowData.creditor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">债务人：</span>
            <span class="value">{{ rowData.debtor_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">调解员：</span>
            <span class="value">{{ rowData.mediator_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">资产包名称：</span>
            <span class="value">{{ rowData.asset_package_name || '-' }}</span>
          </div>
        </div>
      </div>
      <div class="info-section">
        <h3 class="section-title">调解方案详情</h3>
        <template  v-if="data.length > 0">
          <div v-for="(plan, planIndex) in data" :key="planIndex" class="scheme-container">
            <div class="scheme-title">{{ plan.plan_name }}</div>
            <template v-if="plan.plan_config.length > 0">
              <div v-for="(config, configIndex) in plan.plan_config" :key="'config' + configIndex"  class="plan-row">
                <div class="plan-label">{{ config.title }}：</div>
                <div class="plan-value">{{ config.value }}</div>
              </div>
            </template>
          </div>
        </template>
        <div class="empty-state" v-else>
          <p>暂无调解方案数据</p>
        </div>
      </div>
    </div>
    <!-- 底部按钮 -->
    <!-- <div class="dialog-footer">
      <CustomButton @click="handleClose" :height="34">
        <i class="jt-20-delete"></i>关闭
      </CustomButton>
    </div> -->
  </CustomDialog>
</template>

<script lang="ts" setup>
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

interface Props {
  visible: boolean
  rowData: any
  data: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  rowData: null,
  data: null
})

const emit = defineEmits<Emits>()

function handleClose() {
  emit('update:visible', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.preview-content {
  max-height: 700px;
  overflow-y: auto;
  
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          min-width: 120px;
          font-weight: 500;
          color: #666;
        }

        .value {
          color: #333;
          flex: 1;
        }
      }
    }

    .scheme-container{
        background-color: #f9fafb;
        padding: 15px;
        border-radius: 9px;
        margin-bottom: 15px;
      .scheme-title{
        font-weight: bold;
        font-size: 16px;
        // border-bottom: 1px solid #dfdada;
        // padding-bottom: 15px;
      }
      .plan-row{
        display: flex;
        margin: 12px 0;
        gap: 15px;
        .plan-label{
          color: #374151;
          font-weight: 500;
        }
        .plan-value{
          color: #6b7280;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px;
  color: #9ca3af;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>

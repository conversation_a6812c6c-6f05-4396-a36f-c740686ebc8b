/* empty css             */import{C as J,h as R,c as X}from"./headerCellStyle-3128036a.js";/* empty css                     *//* empty css                 *//* empty css                      */import{d as G,r as i,A as H,o as r,e as k,f as c,g as t,J as Q,K as D,q as I,w as u,n as M,F as B,S as W,E as _,L as Y,M as Z,N as ee,B as ae,h as L,ag as te,ah as le,O as se,j as oe,k as ne,X as ie,U as ue,l as ce,p as de,m as re}from"./index-d4ffb1a1.js";import{_ as _e}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{C as F}from"./CustomButton-777ba9d4.js";import{_ as pe}from"./_plugin-vue_export-helper-c27b6911.js";const O=p=>(de("data-v-b2e97966"),p=p(),re(),p),me={class:"case-tracking"},ve={class:"search-area"},ge={class:"search-form"},fe={class:"table-container"},he={class:"operation-buttons"},be=["onClick"],ye={class:"pagination-container"},Ce={class:"edit-form-container"},Ee={class:"dialog-footer"},Ve=O(()=>c("i",{class:"jt-20-ensure"},null,-1)),ke=O(()=>c("i",{class:"jt-20-delete"},null,-1)),we=G({__name:"personnelDispatch",setup(p){const s=i({search:"",page:1,page_size:10}),h=i(!1),w=i([]),x=i(0),b=i(!1),m=i(!1),v=i(null),S=i([]),y=i(!1),o=i({case_number:"",case_status_cn:"",mediator:null,mediator_name:""});function U(){s.value.page=1,g()}async function g(){h.value=!0;const l={page:s.value.page,page_size:s.value.page_size,search:s.value.search},{data:e}=await W(l),{state:n,msg:d}=e;if(n==="success"){const{results:E,count:f}=e.data;w.value=E,x.value=f}else _.error(d);h.value=!1}function K(l){s.value.page=l,g()}function N(l,e){v.value=l,o.value={case_number:l.case_number,case_status_cn:l.case_status_cn,mediator:l.mediator||null,mediator_name:l.mediator_name||""},P(),b.value=!0}async function P(){y.value=!0;const{data:l}=await te(),{state:e,msg:n}=l;e==="success"?S.value=l.data.map(d=>({label:d.username,value:d.id})):_.error(n||"获取调解员数据失败"),y.value=!1}function C(){b.value=!1,v.value=null,o.value={case_number:"",case_status_cn:"",mediator:null,mediator_name:""}}async function T(){if(!v.value||!o.value.mediator){_.error("请选择调解员");return}m.value=!0;const l={mediator:o.value.mediator},{data:e}=await le(l,v.value.id),{state:n,msg:d}=e;n==="success"?(_.success("编辑成功"),C(),g()):_.error(d||"编辑失败"),m.value=!1}return H(()=>{g()}),(l,e)=>{const n=se,d=Y,E=Z,f=oe,V=ne,j=ie,q=ue,$=ce,z=ee;return r(),k(B,null,[c("div",me,[c("div",ve,[c("div",ge,[t(J,{modelValue:s.value.search,"onUpdate:modelValue":e[0]||(e[0]=a=>s.value.search=a),placeholder:"请输入调解案件号",class:"search-input",onKeydown:Q(U,["enter"]),onClick:U},null,8,["modelValue","onKeydown"])])]),c("div",fe,[D((r(),I(d,{data:w.value,style:{width:"100%"},"header-cell-style":M(R),"cell-style":M(X),border:"",stripe:""},{default:u(()=>[t(n,{prop:"case_number",label:"调解案件号",align:"center"}),t(n,{prop:"case_status_cn",label:"案件状态",align:"center"}),t(n,{prop:"mediator_name",label:"调解员",align:"center"}),t(n,{label:"操作",align:"center",width:"180"},{default:u(({row:a,$index:A})=>[c("div",he,[c("div",{onClick:xe=>N(a,A),class:"operation-btn edit-btn"},"编辑",8,be)])]),_:1})]),_:1},8,["data","header-cell-style","cell-style"])),[[z,h.value]])]),c("div",ye,[t(E,{class:"pagi",background:"","current-page":s.value.page,"onUpdate:currentPage":e[1]||(e[1]=a=>s.value.page=a),"page-size":s.value.page_size,"onUpdate:pageSize":e[2]||(e[2]=a=>s.value.page_size=a),total:x.value,layout:"prev, pager, next",onCurrentChange:K},null,8,["current-page","page-size","total"])])]),t(_e,{visible:b.value,"onUpdate:visible":C,width:"600px",title:"编辑人员调度"},{default:u(()=>[D((r(),k("div",Ce,[t($,{model:o.value,"label-width":"120px",class:"edit-form"},{default:u(()=>[t(V,{label:"调解案件号"},{default:u(()=>[t(f,{modelValue:o.value.case_number,"onUpdate:modelValue":e[3]||(e[3]=a=>o.value.case_number=a),disabled:"",class:"readonly-input"},null,8,["modelValue"])]),_:1}),t(V,{label:"案件状态"},{default:u(()=>[t(f,{modelValue:o.value.case_status_cn,"onUpdate:modelValue":e[4]||(e[4]=a=>o.value.case_status_cn=a),disabled:"",class:"readonly-input"},null,8,["modelValue"])]),_:1}),t(V,{label:"调解员",required:""},{default:u(()=>[t(q,{modelValue:o.value.mediator,"onUpdate:modelValue":e[5]||(e[5]=a=>o.value.mediator=a),placeholder:"请选择调解员",loading:y.value,style:{width:"100%"},clearable:""},{default:u(()=>[(r(!0),k(B,null,ae(S.value,a=>(r(),I(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"]),c("div",Ee,[t(F,{onClick:T,loading:m.value,height:34,"btn-type":"blue"},{default:u(()=>[Ve,L("确认 ")]),_:1},8,["loading"]),t(F,{onClick:C,height:34},{default:u(()=>[ke,L("取消 ")]),_:1})])])),[[z,m.value]])]),_:1},8,["visible"])],64)}}});const Oe=pe(we,[["__scopeId","data-v-b2e97966"]]);export{Oe as default};

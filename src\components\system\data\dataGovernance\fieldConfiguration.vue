<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '../../../common/CustomButton.vue'
import CustomInput from '../../../common/CustomInput.vue'
import { headerCellStyle, cellStyle } from "../../../../common/functions/headerCellStyle"
import FieldConfigurationAdd from '../../dialogs/FieldConfigurationAdd.vue'
import FieldConfigurationEdit from '../../dialogs/FieldConfigurationEdit.vue'
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue'
import { getFieldConfig, addFieldConfig, editFieldConfig, deleteFieldConfig, bulkUpdateFieldConfig } from '@/axios/system'
import type { 
  FieldConfig, 
  FieldType, 
  ValidationType, 
  Pagination,
  PageQuery,
  PageResult,
  FieldTypeOption,
  ValidationOption
} from '../../type'
import { FieldType as FieldTypeEnum, ValidationType as ValidationTypeEnum } from '../../type'
import { logButtonClick } from '@/utils/operationLogger'

// 搜索关键字
const search = ref('')
// 响应式数据
const loading = ref(false)
const tableData = ref<FieldConfig[]>([])

// 弹框控制
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const editRow = ref<FieldConfig | null>(null)
const showDeleteDialog = ref(false)
const deleteRow = ref<FieldConfig | null>(null)

// 分页数据
const pagination = reactive<Pagination>({
  page: 1,
  page_size: 9999,
  total: 0
})

// 页面加载时获取数据
onMounted(() => {
  loadTableData()
})

/**
 * 加载表格数据
 */
async function loadTableData() {
  loading.value = true
  const params: PageQuery = {
    page: pagination.page,
    page_size: pagination.page_size,
    search: search.value
  }
  const {data} = await getFieldConfig(params)
  const { state, msg } = data
  if(state === 'success') {
    tableData.value = data.data.results
    // pagination.total = data.data.count
    loading.value = false
  } else {
    ElMessage.error(msg)
    loading.value = false
  } 
}

/**
 * 搜索数据
 */
function handleSearch() {
  logButtonClick('搜索', '数据治理-字段配置') // 记录操作日志
  loadTableData()
}

/**
 * 上移行
 */
async function moveRowUp(index: number) {
  if (index === 0) {
    ElMessage.warning('已经是第一行')
    return
  }
  const temp = tableData.value[index]
  tableData.value[index] = tableData.value[index - 1]
  tableData.value[index - 1] = temp
  // 调用批量更新接口保存排序
  await saveBulkTableData()
}

/**
 * 下移行
 */
async function moveRowDown(index: number) {
  if (index === tableData.value.length - 1) {
    ElMessage.warning('已经是最后一行')
    return
  }
  const temp = tableData.value[index]
  tableData.value[index] = tableData.value[index + 1]
  tableData.value[index + 1] = temp
  // 调用批量更新接口保存排序
  await saveBulkTableData()
}

/**
 * 批量保存表格数据 - 调用bulkUpdateFieldConfig接口
 * 请求数据格式：{ "items": [{"id": 1, "field_name": "姓名", "is_masked": true}, {"id": 2, "field_type": "date", "data_validation": "none"}] }
 */
async function saveBulkTableData() {
  try {
    loading.value = true
    // 构造批量更新的数据格式
    const items = tableData.value.map((item: any, index) => ({
      id: item.id,
      field_name: item.field_name,
      field_type: item.field_type,
      data_validation: item.data_validation || item.validation,
      is_masked: item.is_masked !== undefined ? item.is_masked : item.isDesensitize,
      prefix_keep_chars: item.prefix_keep_chars !== undefined ? item.prefix_keep_chars : item.prefixKeepCount,
      suffix_keep_chars: item.suffix_keep_chars !== undefined ? item.suffix_keep_chars : item.suffixKeepCount,
      display_order: index + 1
    }))
    
    const { data } = await bulkUpdateFieldConfig({ items })
    const { state, msg } = data
    
    if (state === 'success') {
      ElMessage.success(msg)
      // 重新加载数据确保数据同步
      await loadTableData()
    } else {
      ElMessage.error(msg || '保存失败')
    }
  } catch (error) {
    ElMessage.error(error || '保存失败')
  } finally {
    loading.value = false
  }
}
/**
 * 打开新增弹框
 */
function goToAdd() {
  showAddDialog.value = true
}

/**
 * 打开编辑弹框
 */
function goToEdit(row: FieldConfig) {
  editRow.value = { ...row }
  showEditDialog.value = true
}

/**
 * 关闭新增弹框
 */
function handleAddClose() {
  showAddDialog.value = false
}

/**
 * 关闭编辑弹框
 */
function handleEditClose() {
  showEditDialog.value = false
  editRow.value = null
}

/**
 * 新增成功回调
 */
async function handleAddSuccess(formData: FieldConfig) {
  const {data} = await addFieldConfig(formData)
  const { state, msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    showAddDialog.value = false
    loadTableData()
  } else {
    ElMessage.error(msg)
    showAddDialog.value = false
  }
}

/**
 * 编辑成功回调
 */
async function handleEditSuccess(formData: FieldConfig) {
  const {data} = await editFieldConfig(formData, editRow.value.id)
  const { state, msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    showEditDialog.value = false
    editRow.value = null
    loadTableData()
  } else {
    ElMessage.error(msg)
    showEditDialog.value = false
    editRow.value = null
  }
}

/**
 * 打开删除确认弹框
 */
function handleDelete(row: FieldConfig) {
  deleteRow.value = row
  showDeleteDialog.value = true
}

/**
 * 确认删除字段配置
 */
async function confirmDelete() {
  if (!deleteRow.value) return

  try {
    const {data } = await deleteFieldConfig(deleteRow.value.id)
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success(msg)
      loadTableData()
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    ElMessage.error('删除失败，请重试')
  } finally {
    showDeleteDialog.value = false
    deleteRow.value = null
  }
}
</script>

<template>
  <div class="field-configuration">
    <div class="search-header">
	    <CustomInput v-model="search" placeholder="搜索字段名称、类型" @click="handleSearch"></CustomInput>
      <CustomButton @click="goToAdd" :height="34"><i class="jt-20-add"></i>新增字段</CustomButton>
    </div>

    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        border
        style="width: 100%"
		    :cell-style="cellStyle"
        :header-cell-style="headerCellStyle"
        :max-height="730"
      >
		    <el-table-column type="index" label="序号" width="80" align="center"/>
        <el-table-column prop="field_name" label="字段名称" min-width="210" align="center" />
        <el-table-column prop="field_type_cn" label="字段类型" min-width="100" align="center"></el-table-column>
        <el-table-column prop="data_validation_cn" label="数据校验" min-width="100" align="center"></el-table-column>
        <el-table-column label="是否脱敏" min-width="60" align="center">
          <template #default="{ row }">
            {{ row.is_masked ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="prefix_keep_chars" label="前保留字符数" min-width="70" align="center" />
        <el-table-column prop="suffix_keep_chars" label="后保留字符数" min-width="70" align="center" />
        <el-table-column label="操作" width="310" fixed="right" align="center">
          <template #default="{ row,$index }">
            <div class="operation-buttons">
              <div @click="goToEdit(row)" class="operation-btn edit-btn">编辑</div>
              <div @click="moveRowUp($index)" class="operation-btn move-up-btn">上移</div>
              <div @click="moveRowDown($index)" class="operation-btn move-down-btn">下移</div>
              <div @click="handleDelete(row)" class="operation-btn delete-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
     </div>

     <!-- 新增弹框组件 -->
     <FieldConfigurationAdd 
       :visible="showAddDialog"
       @close="handleAddClose"
       @success="handleAddSuccess" />

     <!-- 编辑弹框组件 -->
     <FieldConfigurationEdit
       :visible="showEditDialog"
       :edit-data="editRow"
       @close="handleEditClose"
       @success="handleEditSuccess" />

     <!-- 删除确认弹框 -->
     <DeleteConfirmDialog
       :visible="showDeleteDialog"
       title="删除字段配置"
       :message="`确定要删除字段「${deleteRow?.field_name}」吗？此操作不可撤销。`"
       confirm-text="确定"
       cancel-text="取消"
       @update:visible="showDeleteDialog = $event"
       @confirm="confirmDelete"
       @cancel="showDeleteDialog = false" />
   </div>
</template>

<style lang="scss" scoped>
.field-configuration {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  // 搜索区域样式
  .search-header {
    display: grid;
    grid-template-columns: 300px 120px;
    gap: 20px;
    margin-bottom: 20px;
  }

  // 表格容器样式
  .table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table) {
      border-radius: 8px;
      
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #f5f7fa;
            color: #606266;
            font-weight: 600;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  // Element-Plus 组件样式覆盖
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 4px;
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .field-configuration {
    .page-header {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }

    .search-container {
      :deep(.el-form) {
        .el-form-item {
          display: block;
          margin-bottom: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .field-configuration {
    padding: 10px;
  }
}
</style>
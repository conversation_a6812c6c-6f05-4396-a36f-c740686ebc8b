<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import AssetPackageSelector from "./AssetPackageSelector.vue";
import { ElMessage } from "element-plus";
import type { FieldType, MediationField as BaseMediationField, AddMediationParams, FieldTypeOption, UploadFile, CaseStatusOption } from '../type';
import { FieldType as FieldTypeEnum } from '../type';
import { getDebtor } from '@/axios/system'
/**
 * 扩展的调解字段接口，增加值锁定属性
 */
interface MediationField extends BaseMediationField {
  isValueLocked?: boolean
}

/**
 * 资产包选择结果接口
 */
interface AssetSelectionResult {
  fileName: string
  selectedRow: Record<string, any>
  headers: Array<{ prop: string; label: string; width?: number }>
  // 新增字段
  asset_package: number | null
  creditor_name: string
  creditor: number | null
  asset_package_row_number: number
}

const props = defineProps<{
  showDialog: boolean
  mode?: 'plan' | 'asset' // 新增模式参数
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: AddMediationParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 资产包选择器显示状态
const showAssetSelector = ref(false)

// 调解信息基础信息
const planForm = ref({
  asset_package_name: '',           // 资产包名称
  asset_package: null as number | null,  // 资产包ID
  asset_package_row_number: null as number | null, // 运营数据选中序号
  debtor: '',                  // 债务人
  creditor: null as number | null,  // 债权人ID
  creditor_name: '',           // 债权人名称（用于显示）
})

// 债权人
const debtorOptions = ref([])

// 固定字段 - 相关文件
const relatedFiles: Ref<UploadFile[]> = ref([])


// 动态字段列表
const dynamicFields: Ref<MediationField[]> = ref([])

// 字段类型选项配置（移除文件上传选项）
const fieldTypeOptions: FieldTypeOption[] = [
  { label: '文本类型', value: FieldTypeEnum.TEXTAREA, icon: 'jt-24-edit' },
  { label: '日期类型', value: FieldTypeEnum.DATE, icon: 'jt-24-calendar' },
  { label: '金额类型', value: FieldTypeEnum.AMOUNT, icon: 'jt-24-money' }
]

/**
 * 获取浏览器存储的数据键值对
 */
const storedAssetData = computed(() => {
  const storageKey = `asset_data_${planForm.value.asset_package_name}`
  const stored = localStorage.getItem(storageKey)
  try {
    return stored ? JSON.parse(stored) : {}
  } catch {
    return {}
  }
})

/**
 * 获取可用的字段标题选项（用于文本输入类型的字段）
 */
const availableFieldOptions = computed(() => {
  const data = storedAssetData.value
  return Object.keys(data).map(key => ({
    label: key,
    value: key
  }))
})

// 表单验证规则
const rules = {
  asset_package_name: [
    { required: true, message: '请选择资产包名称', trigger: 'change' },
  ]
}

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 弹框打开时重置表单
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    resetForm()
    loadDebtorOptions()
  }
}

// 获取债务人下拉框
function loadDebtorOptions(){
  getDebtor({page:1,page_size:1000}).then(res => {
    const { state, msg } = res.data
    if(state === 'success') {
      debtorOptions.value = res.data.data.results.map((item: any) => ({
        label: item.debtor_name,
        value: item.id
      }))
    }else{
      ElMessage.error(msg)
    }
  })
}

// 重置表单数据
function resetForm() {
  planForm.value.asset_package_name = ''
  planForm.value.asset_package = null
  planForm.value.asset_package_row_number = null
  planForm.value.debtor = ''
  planForm.value.creditor = null
  planForm.value.creditor_name = ''
  relatedFiles.value = []
  dynamicFields.value = []
}

// 关闭弹框
function close() {
  emit('close')
}

/**
 * 显示资产包选择器
 */
function showAssetPackageSelector() {
  showAssetSelector.value = true
}

/**
 * 处理资产包选择确认
 */
function handleAssetSelectionConfirm(result: AssetSelectionResult) {
  console.log(result,'====result')
  // 设置资产包相关信息
  planForm.value.asset_package_name = result.fileName
  planForm.value.asset_package = result.asset_package
  planForm.value.asset_package_row_number = result.asset_package_row_number

  // 设置债权人信息
  planForm.value.creditor = result.creditor
  planForm.value.creditor_name = result.creditor_name

  console.log('接收到的资产包选择结果:', result)

  // 构造存储数据的键值对格式 - 表头作为key，数据作为value
  const storageData: Record<string, any> = {}
  result.headers.forEach(header => {
    const value = result.selectedRow[header.prop]
    if (value !== undefined && value !== null) {
      storageData[header.label] = value  // 表头label作为key，对应数据作为value
    }
  })

  // 存储到浏览器localStorage
  const storageKey = `asset_data_${result.fileName}`
  localStorage.setItem(storageKey, JSON.stringify(storageData))

  // 默认添加一个文本输入字段
  addField()
}

// 生成唯一字段ID
function generateFieldId(): string {
  return 'GZTJ' + Date.now() + '000'+ Math.random().toString(36).substr(2, 9)
}

// 添加新字段
function addField() {
  const newField: MediationField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    value: '',
    required: false,
    isValueLocked: false // 新增属性，标识值是否被锁定
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 字段类型改变处理
function onFieldTypeChange(field: MediationField) {
  // 重置字段值和锁定状态
  field.value = getDefaultValueByType(field.type)
  field.isValueLocked = false
  field.title = ''
}

/**
 * 判断字段标题是否应该显示为选择器
 * 条件：字段类型=文本输入 + 资产包名称不为空 + 有存储的数据
 */
function shouldShowTitleSelector(field: MediationField): boolean {
  return field.type !== null &&
         planForm.value.asset_package_name !== '' &&
         availableFieldOptions.value.length > 0
}

/**
 * 处理字段标题选择
 */
function onFieldTitleSelect(field: MediationField, selectedKey: string) {
  field.title = selectedKey
  
  // 如果选择的key有对应的value，则自动填充并锁定
  const storedValue = storedAssetData.value[selectedKey]
  if (storedValue !== undefined && storedValue !== null) {
    field.value = storedValue
    field.isValueLocked = true  // 锁定值，禁用编辑
  } else {
    field.value = ''
    field.isValueLocked = false
  }
}

/**
 * 判断字段值是否应该被禁用
 */
function isFieldValueDisabled(field: MediationField): boolean {
  return field.isValueLocked || false
}

// 根据字段类型获取默认值
function getDefaultValueByType(type: FieldType): any {
  switch (type) {
    case FieldTypeEnum.TEXTAREA:
      return ''
    case FieldTypeEnum.DATE:
      return ''
    case FieldTypeEnum.AMOUNT:
      return 0
    default:
      return ''
  }
}

/**
 * 相关文件上传处理 - 支持多文件同时选择
 * @param event 文件选择事件
 */
function handleRelatedFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const fileList = target.files
  
  if (!fileList || fileList.length === 0) return
  
  // 遍历所有选中的文件
  Array.from(fileList).forEach(file => {
    const uploadFile: UploadFile = {
      id: generateFieldId(),
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file), // 创建预览URL
      file: file, // 保存原始文件对象用于FormData上传
      status: 'success'
    }
    
    relatedFiles.value.push(uploadFile)
  })
  
  // 清空input值，允许重复选择相同文件
  target.value = ''
}

/**
 * 触发文件选择器
 */
function triggerFileSelector() {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true // 支持多文件选择
  input.accept = '*' // 不限制文件类型
  input.style.display = 'none'
  
  input.addEventListener('change', handleRelatedFileChange)
  
  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

// 删除相关文件
function removeRelatedFile(fileIndex: number) {
  const file = relatedFiles.value[fileIndex]
  // 释放创建的URL以避免内存泄漏
  if (file.url) {
    URL.revokeObjectURL(file.url)
  }
  relatedFiles.value.splice(fileIndex, 1)
}
/* 
// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const renderData = {
    planTitle: planForm.value.title,
    relatedFiles: relatedFiles.value.map(f => f.name),
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: MediationField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    default:
      return String(field.value || '')
  }
} */

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    if (!field.title.trim()) {
      ElMessage.error(`第${i + 1}个字段的标题不能为空`)
      return false
    }
    
    if (field.required) {
      if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    }
  }
  return true
}

/**
 * 创建FormData对象用于文件上传
 * @returns FormData对象
 */
/* function createFormData(): FormData {
  const formData = new FormData()

  // 添加基础信息
  formData.append('debtor', planForm.value.debtor)
  formData.append('creditor', planForm.value.creditor?.toString() || '')
  formData.append('creditor_name', planForm.value.creditor_name || '')
  formData.append('asset_package_name', planForm.value.asset_package_name)
  formData.append('asset_package', planForm.value.asset_package?.toString() || '')
  formData.append('asset_package_row_number', planForm.value.asset_package_row_number?.toString() || '')
  // formData.append('jsonData', generateWechatRenderData())

  // 添加相关文件
  relatedFiles.value.forEach((fileItem, index) => {
    if (fileItem.file) {
      formData.append(`file`, fileItem.file)
      // formData.append(`relatedFiles_${index}_id`, fileItem.id || '')
      // formData.append(`relatedFiles_${index}_name`, fileItem.name)
    }
  })

  // 添加动态字段数据
  formData.append('mediation_config', JSON.stringify(dynamicFields.value))

  return formData
} */

// 确认添加调解信息
async function ensureAdd() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 检查是否至少有一个字段
    if (dynamicFields.value.length === 0) {
      ElMessage.error('请至少添加一个字段')
      return
    }
    const formData = new FormData()
    // 添加基础信息
    formData.append('debtor', planForm.value.debtor)
    formData.append('creditor', planForm.value.creditor?.toString() || '')
    formData.append('creditor_name', planForm.value.creditor_name || '')
    formData.append('asset_package_name', planForm.value.asset_package_name)
    formData.append('asset_package', planForm.value.asset_package?.toString() || '')
    formData.append('asset_package_row_number', planForm.value.asset_package_row_number?.toString() || '')
    // formData.append('jsonData', generateWechatRenderData())

    // 添加相关文件
    relatedFiles.value.forEach((fileItem, index) => {
      if (fileItem.file) {
        formData.append(`file`, fileItem.file)
      }
    })

    // 添加动态字段数据
    formData.append('mediation_config', JSON.stringify(dynamicFields.value))
    
    emit('ensure', formData)
  } finally {
    loading.value = false
  }
}

/* // 计算字段类型图标类名
function getFieldTypeIcon(type: FieldType): string {
  const option = fieldTypeOptions.find(opt => opt.value === type)
  return option?.icon || 'jt-24-edit'
}
 */
// 获取文件大小显示格式
function formatFileSize(size: number): string {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}
/* 
// 案件号选项数据
const caseNumberOptions = ref([
  { label: 'TJ2024001', value: 'TJ2024001' },
  { label: 'TJ2024002', value: 'TJ2024002' },
  { label: 'TJ2024003', value: 'TJ2024003' }
])

// 资产包名称选项数据  
const assetPackageOptions = ref([
  { label: '债务人基础信息表.xlsx', value: '债务人基础信息表.xlsx' },
  { label: '资产评估明细表.xlsx', value: '资产评估明细表.xlsx' },
  { label: '催收记录表.xlsx', value: '催收记录表.xlsx' },
  { label: '法律文书清单.xlsx', value: '法律文书清单.xlsx' }
]) */
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" title="新增调解信息">
    <div class="add-plan-content">
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="110px">
        <el-form-item label="资产包名称" prop="asset_package_name">
          <el-input
            v-model="planForm.asset_package_name"
            readonly
            placeholder="请点击选择资产包名称"
            @click="showAssetPackageSelector"
            style="cursor: pointer;" />
        </el-form-item>

        <el-form-item label="债权人" prop="creditor_name">
          <el-input
            v-model="planForm.creditor_name"
            disabled />
        </el-form-item>

        <el-form-item label="债务人" prop="debtor">
          <el-select
            v-model="planForm.debtor"
            filterable
            placeholder="请选择债务人"
            style="width: 100%"
            clearable>
            <el-option
              v-for="option in debtorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <div class="fixed-fields-section">
        <div class="fixed-fields-content"> 
          <div class="field-group">
            <label class="field-label">相关文件</label>
            <div class="file-upload-section"> 
              <CustomButton @click="triggerFileSelector" :height="32">
                <i class="jt-20-upload"></i>选择文件
              </CustomButton>
               
              <div v-if="relatedFiles.length > 0" class="file-list">
                <div 
                  v-for="(file, fileIndex) in relatedFiles" 
                  :key="file.id"
                  class="file-item">
                  <span class="file-name" :title="file.name">{{ file.name }}</span>
                  <i 
                    class="jt-20-delete file-remove" 
                    @click="removeRelatedFile(fileIndex)"
                    title="删除文件"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dynamic-fields-section">
        <div class="section-header">
          <h3>调解信息配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>

        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div 
            v-for="(field, index) in dynamicFields" 
            :key="field.id"
            class="field-item">
            
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="32" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <div class="field-config">
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select 
                  v-model="field.type" 
                  @change="onFieldTypeChange(field)"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>
              
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <!-- 文本输入类型且有可选项时显示选择器 -->
                <el-select 
                  v-if="shouldShowTitleSelector(field)"
                  v-model="field.title"
                  @change="onFieldTitleSelect(field, $event)"
                  allow-create
                  filterable
                  placeholder="选择或输入字段标题"
                  style="width: 280px;">
                  <el-option
                    v-for="option in availableFieldOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value" />
                </el-select>
                <el-input 
                  v-else
                  v-model="field.title" 
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>

            <div class="field-preview">
              <label class="config-label">内容预览：</label>
              <div class="preview-content">
                <el-input 
                  v-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  :rows="3"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="'请输入内容'" />
                
                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="'选择日期'"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
                
                <el-input
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  @input="field.value = field.value.replace(/[^0-9.]/g, '')"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="'请输入金额'"
                  style="width: 200px;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置调解信息内容</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="btns-group">
        <CustomButton @click="ensureAdd" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>

  <!-- 资产包选择器 -->
  <AssetPackageSelector 
    :visible="showAssetSelector"
    @update:visible="showAssetSelector = false"
    @confirm="handleAssetSelectionConfirm" />
</template>

<style lang="scss" scoped>
.add-plan-content {
  // 固定字段区域样式
  .fixed-fields-section {
    margin: 20px 0;
    
    .section-header {
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #1377C4;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .fixed-fields-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }
  
  // 动态字段区域样式
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  .field-group{
    display: flex;
    align-items: center;
  }
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 10px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .field-preview {
        .config-label {
          display: block;
          margin-bottom: 12px;
          font-weight: bold;
          color: #333;
          font-size: 15px;
        }
      }
    }
  }
  
  .field-label{
    margin-right: 6px;
    margin-left: 34px;
    width: 7%;
  }
  .file-upload-section{
    width: 100%;
  }
  // 通用文件列表样式
  .file-list {
    max-height: 250px;
    overflow-y: auto;
    // border: 1px solid #e6e6e6;
    
    /* .file-list-header {
      padding: 8px 12px;
      background-color: #f0f0f0;
      border-bottom: 1px solid #e6e6e6;
      font-size: 13px;
      color: #666;
      font-weight: 500;
    } */
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 6px;
      background-color: #F4F5F7;
      transition: all 0.2s ease;
      margin: 15px 0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: #f9f9f9;
      }
      
     /*  .file-icon {
        color: #666;
        font-size: 16px;
        flex-shrink: 0;
      } */
      
      .file-name {
        flex: 1;
        color: #333;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .file-size {
        color: #999;
        font-size: 12px;
        background-color: #f0f0f0;
        padding: 2px 6px;
        border-radius: 10px;
        flex-shrink: 0;
      }
      
      .file-remove {
        color: #f56c6c;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
        flex-shrink: 0;
        
        &:hover {
          color: #fff;
          background-color: #f56c6c;
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
} 

// 响应式适配
@media (max-width: 768px) {
  .add-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>

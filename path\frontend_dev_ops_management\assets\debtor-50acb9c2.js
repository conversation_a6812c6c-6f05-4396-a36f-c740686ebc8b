/* empty css             */import{C as $e,c as Ue,h as xe}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as ne,r as p,v as he,o as r,q as $,w as s,K as ie,e as w,g as a,F as O,B as P,f as i,h as m,G as q,E as z,X as re,U as ue,k as ge,j as be,a2 as Ve,a4 as Ce,l as we,N as de,p as ce,m as pe,aq as me,A as Ee,J as Ae,n as fe,ap as ye,a0 as Oe,ak as Pe,ar as ze,as as De,at as Te,L as je,M as Ie,t as te,au as Se,O as qe}from"./index-d4ffb1a1.js";import{C as se}from"./CustomButton-777ba9d4.js";/* empty css                     *//* empty css                  *//* empty css                 */import{_ as ke}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";import{D as Be}from"./DeleteConfirmDialog-5eb37e5a.js";/* empty css                                                                            */const K=U=>(ce("data-v-c2347703"),U=U(),pe(),U),Le={class:"add-creditor-form"},We={class:"contact-list"},Fe={class:"contact-input-group"},Ne=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Re={class:"contact-list"},Ke={class:"contact-input-group"},Me=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Ge={class:"contact-list"},Je={class:"contact-input-group"},Xe={class:"contact-controls"},He=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Qe={class:"contact-list"},Ye={class:"contact-input-group"},Ze=K(()=>i("i",{class:"el-icon-plus"},null,-1)),el={class:"dialog-footer"},ll=K(()=>i("i",{class:"jt-20-ensure"},null,-1)),al=K(()=>i("i",{class:"jt-20-delete"},null,-1)),tl=ne({__name:"AddDebtor",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(U,{emit:I}){const S=U,E=p(),x=p(!1),b=p({debtor_type:"",debtor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),y=p([{phone:"",phone_type:"",is_primary:!0}]),h=p([{email:"",email_type:"",is_primary:!0}]),g=p([{address:"",address_type:"",is_primary:!0}]),C=p([{wechat:"",wechat_type:"",is_primary:!0}]),T=p([]),D=p([]),A=p(!1);async function N(){A.value=!0;const{data:e}=await me(),{state:l,msg:n}=e;if(l==="success"){const{debtor_types:V,id_types:v}=e.data;if(e.data){const k=Object.entries(V).map(([c,u])=>({label:u,value:c}));T.value=k;const d=Object.entries(v).map(([c,u])=>({label:u,value:c}));D.value=d}}else z.error(n);A.value=!1}const R={debtor_type:[{required:!0,message:"请选择类型",trigger:"change"}],debtor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};he(()=>S.visible,async e=>{e&&(await N(),B())});function B(){b.value={debtor_name:"",debtor_type:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]},y.value=[{phone:"",phone_type:"",is_primary:!0}],h.value=[{email:"",email_type:"",is_primary:!0}],g.value=[{address:"",address_type:"",is_primary:!0}],C.value=[{wechat:"",wechat_type:"",is_primary:!0}],E.value&&E.value.clearValidate()}function F(){I("update:visible")}function j(){y.value.push({phone:"",phone_type:"",is_primary:!1})}function L(e){y.value.length>1&&(y.value.splice(e,1),!y.value.find(l=>l.is_primary)&&y.value.length>0&&(y.value[0].is_primary=!0))}function G(e){y.value.forEach((l,n)=>{l.is_primary=n===e})}function J(){h.value.push({email:"",email_type:"",is_primary:!1})}function X(e){h.value.length>1&&(h.value.splice(e,1),!h.value.find(l=>l.is_primary)&&h.value.length>0&&(h.value[0].is_primary=!0))}function H(e){h.value.forEach((l,n)=>{l.is_primary=n===e})}function Q(){g.value.push({address:"",address_type:"",is_primary:!1})}function Y(e){g.value.length>1&&(g.value.splice(e,1),!g.value.find(l=>l.is_primary)&&g.value.length>0&&(g.value[0].is_primary=!0))}function Z(e){g.value.forEach((l,n)=>{l.is_primary=n===e})}function ee(){C.value.push({wechat:"",wechat_type:"",is_primary:!1})}function le(e){C.value.length>1&&(C.value.splice(e,1),!C.value.find(l=>l.is_primary)&&C.value.length>0&&(C.value[0].is_primary=!0))}function W(e){C.value.forEach((l,n)=>{l.is_primary=n===e})}async function _(){if(E.value){x.value=!0;try{if(!await new Promise(d=>{E.value.validate(c=>{d(c)})}))return;const l=y.value.filter(d=>d.phone.trim()),n=h.value.filter(d=>d.email.trim()),V=g.value.filter(d=>d.address.trim()),v=C.value.filter(d=>d.wechat.trim()),k={...b.value,phones:l.length>0?l:void 0,emails:n.length>0?n:void 0,addresses:V.length>0?V:void 0,wechats:v.length>0?v:void 0};I("confirm",k)}catch(e){console.error("表单验证失败:",e),z.error(e)}finally{x.value=!1}}}return(e,l)=>{const n=re,V=ue,v=ge,k=be,d=Ve,c=Ce,u=we,ae=de;return r(),$(ke,{visible:S.visible,title:"新增债务人",width:"600px","onUpdate:visible":F},{default:s(()=>[ie((r(),w("div",Le,[a(u,{ref_key:"formRef",ref:E,model:b.value,rules:R,"label-width":"120px","label-position":"right"},{default:s(()=>[a(v,{label:"类型",prop:"debtor_type"},{default:s(()=>[a(V,{modelValue:b.value.debtor_type,"onUpdate:modelValue":l[0]||(l[0]=t=>b.value.debtor_type=t),placeholder:"请选择债权人类型",loading:A.value,style:{width:"100%"}},{default:s(()=>[(r(!0),w(O,null,P(T.value,t=>(r(),$(n,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(v,{label:"姓名",prop:"debtor_name"},{default:s(()=>[a(k,{modelValue:b.value.debtor_name,"onUpdate:modelValue":l[1]||(l[1]=t=>b.value.debtor_name=t),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(v,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(V,{modelValue:b.value.id_type,"onUpdate:modelValue":l[2]||(l[2]=t=>b.value.id_type=t),placeholder:"请选择证件类型",loading:A.value,style:{width:"100%"}},{default:s(()=>[(r(!0),w(O,null,P(D.value,t=>(r(),$(n,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(v,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(k,{modelValue:b.value.id_number,"onUpdate:modelValue":l[3]||(l[3]=t=>b.value.id_number=t),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(v,{label:"联系电话"},{default:s(()=>[i("div",We,[(r(!0),w(O,null,P(y.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",Fe,[a(k,{modelValue:t.phone,"onUpdate:modelValue":o=>t.phone=o,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>G(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),y.value.length>1?(r(),$(c,{key:0,onClick:o=>L(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:j,type:"primary",text:"",size:"small"},{default:s(()=>[Ne,m(" 添加电话 ")]),_:1})])]),_:1}),a(v,{label:"联系邮箱"},{default:s(()=>[i("div",Re,[(r(!0),w(O,null,P(h.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",Ke,[a(k,{modelValue:t.email,"onUpdate:modelValue":o=>t.email=o,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>H(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),h.value.length>1?(r(),$(c,{key:0,onClick:o=>X(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:J,type:"primary",text:"",size:"small"},{default:s(()=>[Me,m(" 添加邮箱 ")]),_:1})])]),_:1}),a(v,{label:"联系地址"},{default:s(()=>[i("div",Ge,[(r(!0),w(O,null,P(g.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",Je,[a(k,{modelValue:t.address,"onUpdate:modelValue":o=>t.address=o,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),i("div",Xe,[a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>Z(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),g.value.length>1?(r(),$(c,{key:0,onClick:o=>Y(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])])]))),128)),a(c,{onClick:Q,type:"primary",text:"",size:"small"},{default:s(()=>[He,m(" 添加地址 ")]),_:1})])]),_:1}),a(v,{label:"联系微信"},{default:s(()=>[i("div",Qe,[(r(!0),w(O,null,P(C.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",Ye,[a(k,{modelValue:t.wechat,"onUpdate:modelValue":o=>t.wechat=o,placeholder:"请输入联系微信",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>W(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),C.value.length>1?(r(),$(c,{key:0,onClick:o=>le(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:ee,type:"primary",text:"",size:"small"},{default:s(()=>[Ze,m(" 添加微信 ")]),_:1})])]),_:1})]),_:1},8,["model"]),i("div",el,[a(se,{onClick:_,loading:x.value,height:34,"btn-type":"blue"},{default:s(()=>[ll,m("确认 ")]),_:1},8,["loading"]),a(se,{onClick:F,height:34},{default:s(()=>[al,m("取消 ")]),_:1})])])),[[ae,x.value]])]),_:1},8,["visible"])}}});const sl=_e(tl,[["__scopeId","data-v-c2347703"]]),M=U=>(ce("data-v-cdff7d16"),U=U(),pe(),U),ol={class:"edit-creditor-form"},nl={class:"contact-list"},il={class:"contact-input-group"},rl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),ul={class:"contact-list"},dl={class:"contact-input-group"},cl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),pl={class:"contact-list"},ml={class:"contact-input-group"},_l={class:"contact-controls"},vl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),fl={class:"contact-list"},yl={class:"contact-input-group"},hl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),gl={class:"dialog-footer"},bl=M(()=>i("i",{class:"jt-20-ensure"},null,-1)),Vl=M(()=>i("i",{class:"jt-20-delete"},null,-1)),Cl=ne({__name:"EditDebtor",props:{visible:{type:Boolean,default:!1},creditorData:{default:null}},emits:["update:visible","confirm"],setup(U,{emit:I}){const S=U,E=p(),x=p(!1),b=p({id:0,debtor_type:"",debtor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[],wechat:[]}),y=p([{phone:"",phone_type:"",is_primary:!0}]),h=p([{email:"",email_type:"",is_primary:!0}]),g=p([{address:"",address_type:"",is_primary:!0}]),C=p([{wechat:"",is_primary:!0}]),T=p([]),D=p([]),A=p(!1);async function N(){A.value=!0;const{data:e}=await me(),{state:l,msg:n}=e;if(l==="success"){const{debtor_types:V,id_types:v}=e.data;if(e.data){const k=Object.entries(V).map(([c,u])=>({label:u,value:c}));T.value=k;const d=Object.entries(v).map(([c,u])=>({label:u,value:c}));D.value=d}}else z.error(n);A.value=!1}const R={debtor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],debtor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};he(()=>[S.visible,S.creditorData],async([e,l])=>{e&&l&&typeof l=="object"&&(await N(),B(l))},{immediate:!0});function B(e){b.value={id:e.id,debtor_type:e.debtor_type,debtor_name:e.debtor_name,id_type:e.id_type,id_number:e.id_number,phones:e.phones||[],emails:e.emails||[],addresses:e.addresses||[]},e.phones&&e.phones.length>0?y.value=[...e.phones]:e.phone?y.value=[{phone:e.phone,phone_type:"",is_primary:!0}]:y.value=[{phone:"",phone_type:"",is_primary:!0}],e.emails&&e.emails.length>0?h.value=[...e.emails]:e.email?h.value=[{email:e.email,email_type:"",is_primary:!0}]:h.value=[{email:"",email_type:"",is_primary:!0}],e.addresses&&e.addresses.length>0?g.value=[...e.addresses]:e.address?g.value=[{address:e.address,address_type:"",is_primary:!0}]:g.value=[{address:"",address_type:"",is_primary:!0}],e.wechat&&e.wechat.length>0?C.value=[...e.wechat]:C.value=[{wechat:"",is_primary:!0}],E.value&&E.value.clearValidate()}function F(){I("update:visible")}function j(){y.value.push({phone:"",phone_type:"",is_primary:!1})}function L(e){y.value.length>1&&(y.value.splice(e,1),!y.value.find(l=>l.is_primary)&&y.value.length>0&&(y.value[0].is_primary=!0))}function G(e){y.value.forEach((l,n)=>{l.is_primary=n===e})}function J(){h.value.push({email:"",email_type:"",is_primary:!1})}function X(e){h.value.length>1&&(h.value.splice(e,1),!h.value.find(l=>l.is_primary)&&h.value.length>0&&(h.value[0].is_primary=!0))}function H(e){h.value.forEach((l,n)=>{l.is_primary=n===e})}function Q(){g.value.push({address:"",address_type:"",is_primary:!1})}function Y(e){g.value.length>1&&(g.value.splice(e,1),!g.value.find(l=>l.is_primary)&&g.value.length>0&&(g.value[0].is_primary=!0))}function Z(e){g.value.forEach((l,n)=>{l.is_primary=n===e})}function ee(){C.value.push({wechat:"",is_primary:!1})}function le(e){C.value.length>1&&C.value.splice(e,1)}function W(e){C.value.forEach((l,n)=>{l.is_primary=n===e})}async function _(){if(E.value){x.value=!0;try{if(!await new Promise(d=>{E.value.validate(c=>{d(c)})}))return;const l=y.value.filter(d=>d.phone.trim()),n=h.value.filter(d=>d.email.trim()),V=g.value.filter(d=>d.address.trim()),v=C.value.filter(d=>d.wechat.trim()),k={...b.value,phones:l.length>0?l:void 0,emails:n.length>0?n:void 0,addresses:V.length>0?V:void 0,wechats:v.length>0?v:void 0};I("confirm",k)}catch(e){console.error("表单验证失败:",e),z.error(e)}finally{x.value=!1}}}return(e,l)=>{const n=re,V=ue,v=ge,k=be,d=Ve,c=Ce,u=we,ae=de;return r(),$(ke,{visible:S.visible,title:"编辑债务人",width:"600px","onUpdate:visible":F},{default:s(()=>[ie((r(),w("div",ol,[a(u,{ref_key:"formRef",ref:E,model:b.value,rules:R,"label-width":"120px","label-position":"right"},{default:s(()=>[a(v,{label:"类型",prop:"debtor_type"},{default:s(()=>[a(V,{modelValue:b.value.debtor_type,"onUpdate:modelValue":l[0]||(l[0]=t=>b.value.debtor_type=t),placeholder:"请选择债权人类型",loading:A.value,style:{width:"100%"}},{default:s(()=>[(r(!0),w(O,null,P(T.value,t=>(r(),$(n,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(v,{label:"姓名",prop:"debtor_name"},{default:s(()=>[a(k,{modelValue:b.value.debtor_name,"onUpdate:modelValue":l[1]||(l[1]=t=>b.value.debtor_name=t),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(v,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(V,{modelValue:b.value.id_type,"onUpdate:modelValue":l[2]||(l[2]=t=>b.value.id_type=t),placeholder:"请选择证件类型",loading:A.value,style:{width:"100%"}},{default:s(()=>[(r(!0),w(O,null,P(D.value,t=>(r(),$(n,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(v,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(k,{modelValue:b.value.id_number,"onUpdate:modelValue":l[3]||(l[3]=t=>b.value.id_number=t),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(v,{label:"联系电话"},{default:s(()=>[i("div",nl,[(r(!0),w(O,null,P(y.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",il,[a(k,{modelValue:t.phone,"onUpdate:modelValue":o=>t.phone=o,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>G(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),y.value.length>1?(r(),$(c,{key:0,onClick:o=>L(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:j,type:"primary",text:"",size:"small"},{default:s(()=>[rl,m(" 添加电话 ")]),_:1})])]),_:1}),a(v,{label:"联系邮箱"},{default:s(()=>[i("div",ul,[(r(!0),w(O,null,P(h.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",dl,[a(k,{modelValue:t.email,"onUpdate:modelValue":o=>t.email=o,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>H(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),h.value.length>1?(r(),$(c,{key:0,onClick:o=>X(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:J,type:"primary",text:"",size:"small"},{default:s(()=>[cl,m(" 添加邮箱 ")]),_:1})])]),_:1}),a(v,{label:"联系地址"},{default:s(()=>[i("div",pl,[(r(!0),w(O,null,P(g.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",ml,[a(k,{modelValue:t.address,"onUpdate:modelValue":o=>t.address=o,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),i("div",_l,[a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>Z(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),g.value.length>1?(r(),$(c,{key:0,onClick:o=>Y(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])])]))),128)),a(c,{onClick:Q,type:"primary",text:"",size:"small"},{default:s(()=>[vl,m(" 添加地址 ")]),_:1})])]),_:1}),a(v,{label:"微信号"},{default:s(()=>[i("div",fl,[(r(!0),w(O,null,P(C.value,(t,f)=>(r(),w("div",{key:f,class:"contact-item"},[i("div",yl,[a(k,{modelValue:t.wechat,"onUpdate:modelValue":o=>t.wechat=o,placeholder:"请输入微信号",maxlength:"20",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(d,{modelValue:t.is_primary,"onUpdate:modelValue":o=>t.is_primary=o,label:!0,onChange:o=>W(f),class:"primary-radio"},{default:s(()=>[m("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),C.value.length>1?(r(),$(c,{key:0,onClick:o=>le(f),type:"danger",text:"",size:"small"},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])):q("",!0)])]))),128)),a(c,{onClick:ee,type:"primary",text:"",size:"small"},{default:s(()=>[hl,m(" 添加微信号 ")]),_:1})])]),_:1})]),_:1},8,["model"]),i("div",gl,[a(se,{onClick:_,loading:x.value,height:34,"btn-type":"blue"},{default:s(()=>[bl,m("确认 ")]),_:1},8,["loading"]),a(se,{onClick:F,height:34},{default:s(()=>[Vl,m("取消 ")]),_:1})])])),[[ae,x.value]])]),_:1},8,["visible"])}}});const wl=_e(Cl,[["__scopeId","data-v-cdff7d16"]]),ve=U=>(ce("data-v-85dfb9b8"),U=U(),pe(),U),kl={class:"creditor-management-page"},$l={class:"search-header"},Ul={class:"search-row"},xl={class:"search-item"},El={class:"search-item"},Al=ve(()=>i("label",null,"债务人类型",-1)),Ol={class:"search-item"},Pl=ve(()=>i("label",null,"证件类型",-1)),zl={class:"search-item"},Dl=ve(()=>i("i",{class:"jt-20-add"},null,-1)),Tl={class:"table-container"},jl={class:"operation-buttons"},Il=["onClick"],Sl=["onClick"],ql={key:0,class:"pagination-wrapper"},oe=10,Bl=ne({__name:"debtor",setup(U){const I=p(!1),S=p([]),E=p(0),x=p(1),b=p(""),y=p(""),h=p(""),g=p(!1),C=p(!1),T=p(null),D=p(!1),A=p(null),N=p([]),R=p([]),B=p(!1);async function F(){B.value=!0;const{data:_}=await me(),{state:e,msg:l}=_;if(e==="success"){console.log(_.data,"data.data");const{debtor_types:n,id_types:V}=_.data;if(_.data){const v=Object.entries(n).map(([d,c])=>({label:c,value:d}));N.value=v;const k=Object.entries(V).map(([d,c])=>({label:c,value:d}));R.value=k}}else z.error(l);B.value=!1}async function j(){if(!ye()){z.error("登录状态已失效，请重新登录");return}I.value=!0;const _={page:x.value,page_size:oe};b.value.trim()&&(_.search=b.value.trim()),y.value&&(_.debtor_type=y.value),h.value&&(_.id_type=h.value);const{data:e}=await Oe(_),{state:l,msg:n}=e;l==="success"?(S.value=e.data.results,E.value=e.data.count):z.error(n),I.value=!1}function L(){Pe("搜索","债务人管理"),x.value=1,j()}function G(_){x.value=_,j()}function J(){g.value=!0}function X(){g.value=!1}async function H(_){const{data:e}=await ze(_),{state:l,msg:n}=e;l==="success"&&(z.success(n),g.value=!1,x.value=1,j())}async function Q(_){const{data:e}=await Se(_.id),{state:l,msg:n}=e;l==="success"?(console.log(e.data,"data.data"),T.value=e.data,C.value=!0):z.error(n)}function Y(){C.value=!1,T.value=null}async function Z(_){const{data:e}=await De(_,_.id),{state:l,msg:n}=e;l==="success"&&(z.success(n),C.value=!1,T.value=null,j())}function ee(_){if(!ye()){z.error("登录状态已失效，请重新登录");return}A.value=_,D.value=!0}async function le(){if(A.value)try{await Te(A.value.id),z.success("删除成功"),j()}catch(_){z.error(_)}finally{D.value=!1,A.value=null}}function W(_,e){if(!_||_.length===0)return"";const l=_.find(n=>n.is_primary);return l?l[e]||"":_[0][e]}return Ee(async()=>{await F(),await j()}),(_,e)=>{var c;const l=re,n=ue,V=qe,v=je,k=Ie,d=de;return r(),w("div",kl,[i("div",$l,[i("div",Ul,[i("div",xl,[a($e,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=u=>b.value=u),placeholder:"搜索姓名、证件号码",onKeyup:Ae(L,["enter"]),onClick:L},null,8,["modelValue","onKeyup"])]),i("div",El,[Al,a(n,{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=u=>y.value=u),placeholder:"债务人类型",clearable:"",onChange:L,loading:B.value,style:{width:"200px"}},{default:s(()=>[(r(!0),w(O,null,P(N.value,u=>(r(),$(l,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",Ol,[Pl,a(n,{modelValue:h.value,"onUpdate:modelValue":e[2]||(e[2]=u=>h.value=u),placeholder:"证件类型",clearable:"",onChange:L,loading:B.value,style:{width:"140px"}},{default:s(()=>[(r(!0),w(O,null,P(R.value,u=>(r(),$(l,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",zl,[a(se,{onClick:J,height:34},{default:s(()=>[Dl,m("新增债务人 ")]),_:1})])])]),i("div",Tl,[ie((r(),$(v,{data:S.value,border:"",style:{width:"100%"},"cell-style":fe(Ue),"header-cell-style":fe(xe)},{default:s(()=>[a(V,{type:"index",label:"序号",width:"80",align:"center"},{default:s(({$index:u})=>[m(te(oe*(x.value-1)+u+1),1)]),_:1}),a(V,{label:"类型",width:"140",align:"center",prop:"debtor_type_cn"}),a(V,{align:"center",prop:"debtor_name",label:"姓名","min-width":"200"}),a(V,{label:"证件类型",width:"120",align:"center",prop:"id_type_cn"}),a(V,{align:"center",prop:"id_number",label:"证件号码","min-width":"200"}),a(V,{align:"center",label:"联系电话",width:"130"},{default:s(({row:u})=>[m(te(W(u.phones,"phone")),1)]),_:1}),a(V,{align:"center",label:"联系邮箱",width:"180"},{default:s(({row:u})=>[m(te(W(u.emails,"email")),1)]),_:1}),a(V,{align:"center",label:"联系地址","min-width":"200"},{default:s(({row:u})=>[m(te(W(u.addresses,"address")),1)]),_:1}),a(V,{align:"center",label:"联系微信","min-width":"200"},{default:s(({row:u})=>[m(te(W(u.wechats,"wechat")),1)]),_:1}),a(V,{label:"操作",width:"180",align:"center",fixed:"right"},{default:s(({row:u})=>[i("div",jl,[i("div",{onClick:ae=>Q(u),class:"operation-btn edit-btn"},"编辑",8,Il),i("div",{onClick:ae=>ee(u),class:"operation-btn delete-btn"},"删除",8,Sl)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[d,I.value]]),E.value>0?(r(),w("div",ql,[a(k,{background:"",layout:"prev, pager, next",total:E.value,"current-page":x.value,"page-size":oe,onCurrentChange:G},null,8,["total","current-page"])])):q("",!0)]),a(sl,{visible:g.value,"onUpdate:visible":X,onConfirm:H},null,8,["visible"]),a(wl,{visible:C.value,"creditor-data":T.value,"onUpdate:visible":Y,onConfirm:Z},null,8,["visible","creditor-data"]),a(Be,{visible:D.value,title:"删除债务人",message:`确定要删除债务人「${(c=A.value)==null?void 0:c.debtor_name}」吗？此操作不可撤销。`,"confirm-text":"确认","cancel-text":"取消","onUpdate:visible":e[3]||(e[3]=u=>D.value=u),onConfirm:le,onCancel:e[4]||(e[4]=u=>D.value=!1)},null,8,["visible","message"])])}}});const Zl=_e(Bl,[["__scopeId","data-v-85dfb9b8"]]);export{Zl as default};

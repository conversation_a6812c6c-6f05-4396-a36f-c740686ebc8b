<script lang="ts" setup>
import { onMounted, ref, type Ref, computed } from 'vue'
import CustomInput from '@/components/common/CustomInput.vue';
import CustomButton from '@/components/common/CustomButton.vue';
import MediationInfoPreviewDialog from '../../dialogs/MediationInfoPreviewDialog.vue';
import MediationPlanPreviewDialog from '../../dialogs/MediationPlanPreviewDialog.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { CaseTracking, CaseTrackingParams, CaseTrackingStatus, CaseTrackingStatusOption, UploadFile } from '../../auth/type';
import { CaseTrackingStatus as CaseTrackingStatusEnum } from '../../auth/type';
import { getMediationCase, getDataImportList, getStatusChoices, batchUpdateStatus, getMediationCaseContent, getMediationCasePlanConfig } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 搜索参数
const searchParams: Ref<CaseTrackingParams> = ref({
	search: '',
	page: 1,
	page_size: 10
})

// 是否满足特殊筛选条件（资产包已选择且案件状态为"待发起"）
const isSpecialFilterCondition = computed(() => {
	return assetPackageFilter.value && caseStatusFilter.value === 'draft'
})

const loading = ref(false)
// 表格数据
const tableData = ref([])
const total = ref(0)

// 选择和发起功能相关
const selectedRows = ref<any[]>([]) // 选中的行数据
const isAllSelected = ref(false) // 是否全选

// 筛选功能相关
const assetPackageFilter = ref('') // 资产包筛选
const caseStatusFilter = ref('') // 案件状态筛选
const assetPackageOptions = ref<Array<{label: string, value: string}>>([]) // 资产包选项
const caseStatusOptions = ref<Array<{label: string, value: string}>>([]) // 案件状态选项

// 预览弹框相关
const showMediationInfoPreview = ref(false) // 预览调解信息弹框
const showMediationPlanPreview = ref(false) // 预览调解方案内容弹框
const previewRowData = ref<any>(null) // 预览行数据
const previewData = ref<any>(null) // 预览数据

function handleSearch() {
  searchParams.value.page = 1
  searchCaseTrackingList()
}

// 选择和发起功能相关方法

// Element Plus selection 事件处理
function handleSelectionChange(selection: any[]) {
	selectedRows.value = selection
	updateAllSelectedStatus()
}

function handleSelectAll(selection: any[]) {
	selectedRows.value = selection
	updateAllSelectedStatus()
}

function handleRowSelect(selection: any[], row: any) {
	selectedRows.value = selection
	updateAllSelectedStatus()
}

function updateAllSelectedStatus() {
	// 在特殊筛选条件下，更新全选状态
	if (isSpecialFilterCondition.value) {
		const selectableRows = tableData.value.filter((row: any) =>
			row.case_status === 'draft'
		)
		isAllSelected.value = selectableRows.length > 0 &&
			selectedRows.value.length === selectableRows.length
	}
}

function isRowSelectable(row: any) {
	return row.case_status === 'draft'
}

// 发起按钮是否可用 - 资产包必选，案件状态=待发起才可以点击选择（蓝色）
const isInitiateEnabled = computed(() => {
	// 资产包必选，案件状态必须为"待发起"（draft）
	if (!assetPackageFilter.value || caseStatusFilter.value !== 'draft') {
		return false
	}
	// 在特殊筛选条件下，需要选择案件才能发起
	if (isSpecialFilterCondition.value) {
		return selectedRows.value.length > 0
	}
	// 其他情况下，发起按钮可用
	return true
})

async function handleInitiate() {
	// 检查筛选条件：资产包必选，案件状态必须为"待发起"
	if (!assetPackageFilter.value) {
		ElMessage.warning('请先选择资产包')
		return
	}
	if (caseStatusFilter.value !== 'draft') {
		ElMessage.warning('请将案件状态设置为"待发起"')
		return
	}

	// 在特殊筛选条件下，需要检查是否选择了案件
	if (isSpecialFilterCondition.value && selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要发起的案件')
		return
	}

	try {
		// 批量操作实现：调用 batchUpdateStatus 接口
		if (isSpecialFilterCondition.value) {
			// 提取选中案件的ID列表
			const case_ids = selectedRows.value.map(row => row.id)

			const { data } = await batchUpdateStatus({ case_ids })
			const { state, msg } = data

			if (state === 'success') {
				ElMessage.success(`已成功发起 ${case_ids.length} 个案件`)
				// 清空选择并刷新列表
				selectedRows.value = []
				isAllSelected.value = false
				searchCaseTrackingList()
			} else {
				ElMessage.error(msg || '发起操作失败')
			}
		} else {
			ElMessage.success('发起操作已执行')
		}
	} catch (error) {
		console.error('发起操作失败:', error)
		ElMessage.error('发起操作失败，请重试')
	}
}
// 调用列表接口
async function searchCaseTrackingList() {
	loading.value = true
    const params: any = {
      page: searchParams.value.page,
      page_size: isSpecialFilterCondition.value ? 9999 : searchParams.value.page_size, // 特殊条件下设置大页码
      search: searchParams.value.search
    }

    // 添加筛选参数
    if (assetPackageFilter.value) {
      params.asset_package_name = assetPackageFilter.value
    }
    if (caseStatusFilter.value) {
      params.case_status = caseStatusFilter.value
    }

    const { data } = await getMediationCase(params)
    const { state, msg } = data

    if (state === 'success') {
      const { results, count } = data.data
      tableData.value = results
      total.value = count
      // 更新全选状态
      updateAllSelectedStatus()
    } else {
      ElMessage.error(msg)
    }
	loading.value = false
}

// 加载资产包选项
async function loadAssetPackageOptions() {
  try {
    const { data } = await getDataImportList({ page: 1, page_size: 1000, package_status: 'available' })
    const { state, msg } = data
    if (state === 'success') {
      assetPackageOptions.value = data.data.results.map((item: any) => ({
        label: item.package_name,
        value: item.package_name
      }))
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error('加载资产包选项失败:', error)
  }
}

// 加载案件状态选项 - 调用接口获取
async function loadCaseStatusOptions() {
    const { data } = await getStatusChoices()
    const { state, msg } = data
    if (state === 'success') {
      caseStatusOptions.value = data.data.case_status_choices
    } else {
      ElMessage.error(msg)
    }
}

// 预览功能相关方法
async function previewMediationInfo(row: any) {
  try {
    // 基本数据从列表获取，调解信息配置和相关文件从接口获取
    const { data } = await getMediationCaseContent(row.id)
    const { state, msg } = data

    if (state === 'success') {
      // 合并基本信息和接口数据
      previewData.value = {
        ...row, // 基本数据从列表获取
        ...data.data // 调解信息配置和相关文件从接口获取
      }
      showMediationInfoPreview.value = true
    } else {
      ElMessage.error(msg || '获取调解信息失败')
    }
  } catch (error) {
    console.error('获取调解信息失败:', error)
    ElMessage.error('获取调解信息失败，请重试')
  }
}

async function previewMediationPlan(row: any) {
  try {
    // 基本数据从列表获取，调解方案详情从接口获取
    const { data } = await getMediationCasePlanConfig(row.id)
    const { state, msg } = data

    if (state === 'success') {
	  previewRowData.value = row; // 基本数据从列表获取
	  previewData.value = data.data; // 调解方案详情从接口获取
      showMediationPlanPreview.value = true
    } else {
      ElMessage.error(msg || '获取调解方案失败')
    }
  } catch (error) {
    console.error('获取调解方案失败:', error)
    ElMessage.error('获取调解方案失败，请重试')
  }
}

function closeMediationInfoPreview() {
  showMediationInfoPreview.value = false
  previewData.value = null
}

function closeMediationPlanPreview() {
  showMediationPlanPreview.value = false
  previewRowData.value = null
  previewData.value = null
}

// 分页改变
function handlePageChange(p: number) {
  searchParams.value.page = p
	// 调用列表接口
	searchCaseTrackingList()
}

onMounted(() => {
	searchCaseTrackingList()
	loadAssetPackageOptions()
	loadCaseStatusOptions()
})

</script>

<template>
	<div class="case-tracking">
		<div class="search-header">
			<div class="search-row">
				<div class="search-item">
					<CustomInput
						v-model="searchParams.search"
						placeholder="请输入调解案件号"
						class="search-input"
						@keydown.enter="handleSearch"
						@click="handleSearch"
					/>
				</div>

				<div class="search-item">
					<label>资产包</label>
					<el-select
						v-model="assetPackageFilter"
						placeholder="资产包"
						clearable
						@change="handleSearch">
						<el-option
							v-for="option in assetPackageOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value" />
					</el-select>
				</div>

				<div class="search-item">
					<label>案件状态</label>
					<el-select
						v-model="caseStatusFilter"
						placeholder="案件状态"
						clearable
						@change="handleSearch">
						<el-option
							v-for="option in caseStatusOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value" />
					</el-select>
				</div>
			</div>
			<div class="search-row">
				<CustomButton
					@click="handleInitiate"
					:height="34"
					:disabled="!isInitiateEnabled"
					btn-type="blue">
					发起
				</CustomButton>
			</div>
		</div>

		<div class="table-container">
			<el-table
        		v-loading="loading"
				:data="tableData"
				style="width: 100%"
				:header-cell-style="headerCellStyle"
				:cell-style="cellStyle"
				border
				stripe
				@selection-change="handleSelectionChange"
				@select-all="handleSelectAll"
				@select="handleRowSelect"
			>
				<!-- 复选框列（特殊筛选条件下显示） -->
				<el-table-column
					v-if="isSpecialFilterCondition"
					type="selection"
					width="55"
					:selectable="isRowSelectable" />

				<el-table-column label="序号" type="index" width="80" align="center">
					<template v-slot="{$index}">
						{{ searchParams.page_size * (searchParams.page - 1) + $index + 1 }}
					</template>
				</el-table-column>
				<el-table-column prop="case_number" label="调解案件号" align="center" width="210"></el-table-column>
				<el-table-column prop="creditor_name" label="债权人" align="center" width="200"></el-table-column>
				<el-table-column prop="debtor_name" label="债务人" align="center" width="200"></el-table-column>
				<el-table-column prop="asset_package_name" label="资产包名称" align="center" width="200"></el-table-column>
				<el-table-column prop="case_status_cn" label="案件状态" align="center" width="100"></el-table-column>
				<el-table-column prop="mediator_name" label="调解员" align="center" width="100"></el-table-column>
				<el-table-column prop="file_cn" label="相关文件" align="left" header-align="center" min-width="150">
					<template v-slot="{row}">
						<div style="white-space: pre-wrap !important;">{{ row.file_cn }}</div>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="180" align="center" header-align="center">
					<template v-slot="{ row }">
						<div class="operation-buttons">
							<div @click="previewMediationInfo(row)" class="operation-btn preview-btn">预览调解信息内容</div>
							<div @click="previewMediationPlan(row)" class="operation-btn preview-btn">预览调解方案内容</div>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 分页组件 -->
		<div class="pagination-container">
			<el-pagination
				class="page"
				background
				v-model:current-page="searchParams.page"
				v-model:page-size="searchParams.page_size"
				:total="total"
				layout="prev, pager, next"
				@current-change="handlePageChange"
			/>
		</div>
	</div>

	<!-- 预览调解信息弹框 -->
	<MediationInfoPreviewDialog
		:visible="showMediationInfoPreview"
		:data="previewData"
		@update:visible="showMediationInfoPreview = $event"
		@close="closeMediationInfoPreview"
	/>

	<!-- 预览调解方案内容弹框 -->
	<MediationPlanPreviewDialog
		:visible="showMediationPlanPreview"
		:rowData="previewRowData"
		:data="previewData"
		@update:visible="showMediationPlanPreview = $event"
		@close="closeMediationPlanPreview"
	/>
</template>

<style lang="scss" scoped>
.case-tracking{
	margin: 24px;
	background-color: #fff;
	height: calc(100% - 65px);
	padding: 20px 20px 0 20px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	.search-header{
		margin-bottom: 20px;
		display: flex;
		justify-content: space-between;

		.search-row {
			display: flex;
			align-items: center;
			gap: 20px;
			.search-item {
				display: flex;
				align-items: center;
				gap: 8px;

				.search-input {
					width: 300px;
				}
				label {
					min-width: 50px;
					font-weight: 500;
					color: #333;
				}
				
				:deep(.el-select) {
					width: 160px;
				}
				
				:deep(.el-date-editor) {
					height: 36px;
				}
			}
		}
	}
	.pagination-container{
		margin-top: 20px;
	}
	:deep(.el-pagination){
		justify-content: center;
	}

	.operation-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		justify-content: flex-start;
	}
}
.fields-preview {
    .field-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
      
      .field-type-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        font-size: 12px;
        color: #0369a1;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .more-fields {
        padding: 2px 6px;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
.outbound-e-seal{
	.no_content {
	text-align: center;
	margin: 17% 0;
	color: #7a7a7a;
	font-size: 14px;
		div {
			padding-top: 24px;
		}
	}
}
</style>
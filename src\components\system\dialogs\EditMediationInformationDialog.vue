<script lang="ts" setup>
import { ref, type Ref, watch, computed, onMounted, nextTick } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import FileUpload from "@/components/common/FileUpload.vue";
import { ElMessage } from "element-plus";
import type { FieldType, MediationField, UploadFile } from '../type';
import { FieldType as FieldTypeEnum, FieldConfigOption,DebtorFieldOption } from '../type';
import { getDebtor, editMediationCase, getDataImportDetail, getMediationCaseDetail, getExpressionVariables, parseExpression, expressionCalculation } from '@/axios/system';

/**
 * 扩展的上传文件接口，增加服务器文件标识
 */
interface ExtendedUploadFile extends UploadFile {
  isServerFile?: boolean
  file?: File
  url?: string
}

/**
 * 扩展的调解字段接口，支持表达式编辑
 */
interface ExtendedMediationField extends MediationField {
  id: string
  title: string           // 字段标题
  type: FieldType         // 字段类型
  expression: any         // 表达式内容 - 必需属性，与基接口保持一致
  preview: any            // 预览内容 - 必需属性，与基接口保持一致
  logic_type: string      // 逻辑处理类型 - 必需属性，与基接口保持一致
  // 扩展属性
  value?: any             // 字段值
  mapped_field_config?: {
    id: number
    field_name: string
  } | null
  mapped_debtor_field_config?: string | null
}

const props = defineProps<{
  visible: boolean,
  rowData: any,  // 要编辑的行数据
  editMode: 'asset' | 'mediation'  // 编辑模式
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void,
  (e: 'confirm', formData: FormData): void
}>()

// 表单引用
const formRef = ref()
const loading = ref(false)
const detailLoading = ref(false)

// 表单数据
const formData = ref({
  debtor: '',
  creditor: null as number | null,
  creditor_name: '',
  asset_package_name: '',
  field_mappings_detail: [] as ExtendedMediationField[],
  attachments: [] as ExtendedUploadFile[]
})

// 字段配置选项数据（用于下拉框）
const fieldConfigOptions = ref<FieldConfigOption[]>([])
// const fieldConfigLoading = ref(false)

// // 债务人字段选项数据
// const debtorFieldOptions = ref<DebtorFieldOption[]>([])
// const debtorFieldLoading = ref(false)

// 债务人选项
const debtorOptions = ref<Array<{label: string, value: number}>>([])

// 相关文件
const relatedFiles: Ref<ExtendedUploadFile[]> = ref([])

// 动态字段列表
const dynamicFields: Ref<ExtendedMediationField[]> = ref([])

// 表达式编辑相关数据
const expressionVariables = ref<Array<{code: string, name: string}>>([])
// const expressionLoading = ref(false)
const previewContent = ref('')
const previewLoading = ref(false)

// 自定义表达式编辑器相关状态
const showVariableDropdown: Ref<Record<number, boolean>> = ref({})
const filteredVariables: Ref<Record<number, Array<{ code: string, name: string }>>> = ref({})
const cursorPosition: Ref<Record<number, number>> = ref({})
const variableSearchText: Ref<Record<number, string>> = ref({})

// 计算属性：对话框标题
const dialogTitle = computed(() => {
  return props.editMode === 'asset' ? '编辑资产包信息' : '编辑调解案件信息'
})

// 计算属性：可编辑字段
const editableFields = computed(() => {
  if (props.editMode === 'asset') {
    return ['field_mappings_detail', 'attachments']  // 资产包模式：只能编辑调解信息配置和相关文件
  } else {
    return ['debtor', 'attachments']  // 调解案件模式：只能编辑债务人和相关文件
  }
})

// 监听弹框显示状态，进行数据回显
watch(() => props.visible, (newVal) => {
  if (newVal && props.rowData) {
    loadDetailData()
    getDebtorOptions()
    // 如果是资产包模式，加载表达式变量
    /* if (props.editMode === 'asset') {
      loadExpressionVariables()
    } */
  }
}, { immediate: true })

// 监听行数据变化，进行数据回显
watch(() => props.rowData, (newVal) => {
  if (newVal && props.visible) {
    loadDetailData()
  }
}, { deep: true })

/**
 * 根据编辑模式加载详情数据
 */
async function loadDetailData() {
  if (!props.rowData || !props.rowData.id) return

  detailLoading.value = true
  try {
    let detailData: any = null

    // 根据编辑模式调用不同的详情接口
    if (props.editMode === 'asset') {
      // 资产包模式：调用 getDataImportDetail 接口
      const response = await getDataImportDetail(Number(props.rowData.id))
      const {data:detail} = response.data
      detailData = detail
    } else {
      // 调解案件模式：调用 getMediationCaseDetail 接口
      const response = await getMediationCaseDetail(Number(props.rowData.id))
      const {data:detail} = response.data
      detailData = detail
    }

    // 使用详情数据进行回显
    if (detailData) {
      loadRowData(detailData)
    }
  } catch (error) {
    console.error('加载详情数据失败:', error)
    ElMessage.error('加载详情数据失败，请重试')
  } finally {
    detailLoading.value = false
  }
}

/**
 * 加载行数据进行回显
 * @param rowData 行数据
 */
function loadRowData(rowData: any) {
  if (!rowData) return

  console.log('加载编辑数据:', rowData) // 调试日志

  // 基础信息回显
  formData.value.debtor = rowData.debtor || ''
  formData.value.creditor = rowData.creditor
  formData.value.creditor_name = rowData.creditor_name || ''
  formData.value.asset_package_name = rowData.asset_package_name || rowData.package_name || ''

  // 文件回显处理 - 标记为服务器文件
  relatedFiles.value = rowData.attachments ? JSON.parse(JSON.stringify(rowData.attachments)).map((file: UploadFile) => ({
    ...file,
    isServerFile: true
  } as ExtendedUploadFile)) : []

  // 动态字段深拷贝
  formData.value.field_mappings_detail = rowData.field_mappings_detail || []

  // 处理调解配置字段（用于表达式编辑）
  if (rowData.mediation_config && Array.isArray(rowData.mediation_config)) {
    dynamicFields.value = rowData.mediation_config.map((config: any) => {
      // 确保逻辑处理类型有正确的默认值
      let logicType = config.logic_type
      if (!logicType || logicType === '' || logicType === null || logicType === undefined) {
        logicType = 'text_format'
      }

      return {
        id: config.id || generateFieldId(),
        title: config.title || '',
        type: config.type || FieldTypeEnum.TEXTAREA,
        // 将后端的大括号格式转换为用户友好的显示格式
        expression: convertBracesToDisplayFormat(config.expression || ''),
        preview: config.preview || '',
        // 强制设置默认值
        logic_type: logicType,
      }
    })
  } else {
    dynamicFields.value = []
  }

  // 确保所有字段都有正确的默认逻辑处理类型
  nextTick(() => {
    dynamicFields.value.forEach((field, index) => {
      if (!field.logic_type || field.logic_type === '' || field.logic_type === null || field.logic_type === undefined) {
        // 使用Vue 3的响应式API强制更新
        field.logic_type = 'text_format'
      }
    })

    // 强制触发整个数组的响应式更新
    dynamicFields.value = [...dynamicFields.value]

    console.log('数据回显完成，默认逻辑处理类型已设置:', dynamicFields.value.map(f => ({ id: f.id, logic_type: f.logic_type })))

    // 自定义表达式编辑器已完全支持中文输入
    console.log('数据回显完成，自定义表达式编辑器已启用')

    // 多次延迟确认，确保DOM更新完成
    setTimeout(() => {
      console.log('第一次延迟检查单选框状态:', dynamicFields.value.map(f => ({ id: f.id, logic_type: f.logic_type })))

      // 再次强制更新，使用更激进的方法
      dynamicFields.value.forEach((field, index) => {
        if (!field.logic_type || field.logic_type === '' || field.logic_type === null || field.logic_type === undefined) {
          field.logic_type = 'text_format'
        }
        // 强制触发响应式更新
        const temp = field.logic_type
        field.logic_type = ''
        nextTick(() => {
          field.logic_type = temp || 'text_format'
        })
      })
    }, 100)

    setTimeout(() => {
      console.log('第二次延迟检查单选框状态:', dynamicFields.value.map(f => ({ id: f.id, logic_type: f.logic_type })))

      // 最终确认，确保所有字段都有正确的默认值
      dynamicFields.value.forEach(field => {
        if (!field.logic_type || field.logic_type === '') {
          field.logic_type = 'text_format'
          console.log('最终修正字段默认值:', field.id, field.logic_type)
        }
      })
    }, 500)
  })
  // 资产包tab（配置变量）
  if (props.editMode === 'asset') {
    // mapped_field_names: ["借款人名称", "借款人身份证号码"]
    expressionVariables.value = rowData.mapped_field_names.map((name: string) => ({
      code: name,
      name: name, // 显示中文变量名
      // value: 0 // 默认值
    }))
  }
}

// 获取债务人选项
function getDebtorOptions() {
  getDebtor({ page: 1, page_size: 1000 }).then(res => {
    const { state, msg } = res.data
    if (state === 'success') {
      debtorOptions.value = res.data.data.results.map((item: any) => ({
        label: item.debtor_name,
        value: item.id
      }))
    } else {
      ElMessage.error(msg)
    }
  })
}

// 自定义表达式编辑器相关函数

// 转换存储格式到显示格式：保持{变量名}格式用于视觉区分
function convertStorageToDisplay(expression: string): string {
  if (!expression) return ''

  // 只移除@符号，保持{变量名}格式
  return expression.replace(/@/g, '')
}

// 转换显示格式到存储格式：变量名 -> {变量名}
function convertDisplayToStorage(expression: string): string {
  if (!expression) return ''

  console.log('convertDisplayToStorage 开始转换:', expression)
  console.log('可用变量列表:', expressionVariables.value.map(v => v.name))

  let result = expression

  // 1. 先处理@符号后跟变量名的情况：@变量名 -> {变量名}
  expressionVariables.value.forEach(variable => {
    const variableName = variable.name
    const escapedVariableName = variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const atVariableRegex = new RegExp(`@${escapedVariableName}\\b`, 'g')
    const beforeReplace = result
    result = result.replace(atVariableRegex, `{${variableName}}`)
    if (beforeReplace !== result) {
      console.log(`转换@变量: @${variableName} -> {${variableName}}`)
    }
  })

  // 2. 移除剩余的孤立@符号
  result = result.replace(/@/g, '')

  // 如果清理后的表达式为空或只有空白字符，返回空字符串
  if (!result.trim()) {
    console.log('显示格式转存储格式 - 空表达式:', expression, '->', '')
    return ''
  }

  // 3. 处理直接输入的变量名（不在大括号内的）
  const sortedVariables = [...expressionVariables.value].sort((a, b) => b.name.length - a.name.length)

  sortedVariables.forEach(variable => {
    const variableName = variable.name
    const bracketFormat = `{${variableName}}`

    // 创建正则表达式，匹配独立的变量名（不在大括号内的）
    const escapedVariableName = variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const regex = new RegExp(`(?<!\\{[^}]*)\\b${escapedVariableName}\\b(?![^{]*\\})`, 'g')

    result = result.replace(regex, bracketFormat)
  })

  console.log('显示格式转存储格式:', expression, '->', result)
  return result
}

// 检查表达式中是否包含完整的变量名
function hasCompleteVariable(expression: string): boolean {
  // 移除@符号后检查
  const cleanExpression = expression.replace(/@/g, '')

  return expressionVariables.value.some(variable => {
    // 检查是否包含完整的变量名（使用词边界确保完整匹配）
    const escapedVariableName = variable.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const regex = new RegExp(`\\b${escapedVariableName}\\b`)
    return regex.test(cleanExpression)
  })
}

// 验证表达式格式，确保变量都是{变量名}格式
function validateExpressionFormat(expression: string): boolean {
  if (!expression) return true

  // 检查是否所有变量都被正确包裹在大括号中
  for (const variable of expressionVariables.value) {
    const variableName = variable.name
    const escapedVariableName = variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    // 检查是否存在未包裹的变量名
    const unWrappedRegex = new RegExp(`(?<!\\{[^}]*)\\b${escapedVariableName}\\b(?![^{]*\\})`, 'g')
    if (unWrappedRegex.test(expression)) {
      console.log('发现未包裹的变量:', variableName)
      return false
    }
  }

  return true
}

// 获取表达式placeholder文本
function getExpressionPlaceholder(expression: string) {
  if (!expression || expression.trim() === '') {
    return '请输入表达式，支持中文、数字、运算符（+ - * / =），输入@可选择变量'
  }
  return ''
}

// 格式化表达式用于显示 - 高亮变量
function formatExpressionForDisplay(expression: string): string {
  if (!expression) return ''

  // 将{变量名}替换为带高亮样式的HTML
  return expression.replace(/\{([^}]+)\}/g, '<span class="variable-highlight">{$1}</span>')
}

// 处理表达式输入事件
function handleExpressionInput(displayValue: string, fieldIndex: number) {
  console.log('输入事件 - 显示值:', displayValue)

  // 使用processChineseVariables转换为存储格式并保存
  const storageValue = processChineseVariables(displayValue)
  console.log('输入事件 - processChineseVariables转换后的存储值:', storageValue)

  dynamicFields.value[fieldIndex].expression = storageValue

  // 调用原有的input处理器
  createExpressionChangeHandler(fieldIndex)(storageValue)
}

// 处理表达式失焦事件（新版本）
function handleExpressionBlurEvent(event: Event, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement
  const displayValue = target.value

  console.log('失焦事件 - 输入框显示值:', displayValue)

  // 获取当前数据模型中的存储值
  const currentStorageValue = dynamicFields.value[fieldIndex]?.expression || ''
  console.log('失焦事件 - 当前存储值:', currentStorageValue)

  // 如果输入框的值和当前存储值的显示格式不一致，说明用户手动修改了内容
  const currentDisplayValue = convertStorageToDisplay(currentStorageValue)
  console.log('失焦事件 - 当前显示值:', currentDisplayValue)

  let finalStorageValue = currentStorageValue

  if (displayValue !== currentDisplayValue) {
    // 用户手动修改了内容，需要重新转换
    console.log('失焦事件 - 检测到手动修改，使用processChineseVariables转换')
    finalStorageValue = processChineseVariables(displayValue)
    console.log('失焦事件 - processChineseVariables转换后的存储值:', finalStorageValue)

    // 更新数据模型
    dynamicFields.value[fieldIndex].expression = finalStorageValue
  } else {
    // 没有手动修改，但确保当前存储值经过processChineseVariables处理
    console.log('失焦事件 - 无手动修改，确保存储值格式正确')
    finalStorageValue = processChineseVariables(currentStorageValue)
    console.log('失焦事件 - processChineseVariables处理后的存储值:', finalStorageValue)

    // 更新数据模型
    dynamicFields.value[fieldIndex].expression = finalStorageValue
  }

  // 验证存储格式中的变量都是{变量名}格式
  const hasValidFormat = validateExpressionFormat(finalStorageValue)
  console.log('失焦事件 - 表达式格式验证:', hasValidFormat)

  // 隐藏变量选择下拉框（如果还在显示）
  hideVariableDropdown(fieldIndex)

  // 调用原有的blur处理器，传递存储格式的值（确保是{变量名}格式）
  console.log('失焦事件 - 传递给createExpressionBlurHandler的值:', finalStorageValue)
  createExpressionBlurHandler(fieldIndex)(finalStorageValue)
}

// 处理键盘输入事件
function handleExpressionKeydown(event: KeyboardEvent, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement

  // 记录光标位置（基于显示格式）
  cursorPosition.value[fieldIndex] = target.selectionStart || 0

  // 检测@符号输入
  if (event.key === '@') {
    // 显示变量选择下拉框
    showVariableDropdown.value[fieldIndex] = true
    filteredVariables.value[fieldIndex] = expressionVariables.value
    variableSearchText.value[fieldIndex] = ''
    console.log('显示变量选择下拉框')
  } else if (event.key === 'Escape') {
    // ESC键隐藏下拉框
    hideVariableDropdown(fieldIndex)
  }
}

// 搜索变量
function searchVariables(searchText: string, fieldIndex: number) {
  if (!searchText || searchText.trim() === '') {
    filteredVariables.value[fieldIndex] = expressionVariables.value
  } else {
    const filtered = expressionVariables.value.filter(variable =>
      variable.name.toLowerCase().includes(searchText.toLowerCase()) ||
      variable.code.toLowerCase().includes(searchText.toLowerCase())
    )
    filteredVariables.value[fieldIndex] = filtered
  }
}

// 隐藏变量选择下拉框
function hideVariableDropdown(fieldIndex: number) {
  showVariableDropdown.value[fieldIndex] = false
  filteredVariables.value[fieldIndex] = []
}

// 显示所有变量
function showAllVariables(fieldIndex: number) {
  showVariableDropdown.value[fieldIndex] = true
  filteredVariables.value[fieldIndex] = expressionVariables.value
}

// 在光标位置插入变量
function insertVariableAtCursor(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return

  console.log('=== 开始插入变量 ===')
  console.log('变量信息:', variable)
  console.log('字段索引:', fieldIndex)
  console.log('当前字段数据:', dynamicFields.value[fieldIndex])

  const field = dynamicFields.value[fieldIndex]

  // 获取当前显示格式的表达式
  const currentDisplayExpression = convertStorageToDisplay(field.expression || '')
  console.log('当前显示表达式:', currentDisplayExpression)

  // 获取光标位置，如果没有记录则使用表达式末尾
  const cursorPos = cursorPosition.value[fieldIndex] !== undefined
    ? cursorPosition.value[fieldIndex]
    : currentDisplayExpression.length

  console.log('光标位置:', cursorPos)

  // 构建新的显示格式表达式，在光标位置插入变量
  const beforeCursor = currentDisplayExpression.substring(0, cursorPos)
  const afterCursor = currentDisplayExpression.substring(cursorPos)

  console.log('光标前:', beforeCursor, '光标后:', afterCursor)

  // 如果光标前面是@符号，替换它
  const finalBefore = beforeCursor.endsWith('@') ? beforeCursor.slice(0, -1) : beforeCursor
  const variableName = `{${variable.name}}`  // 显示格式：{变量名}保持大括号包裹
  let newDisplayExpression = finalBefore + variableName + afterCursor

  // 移除表达式中剩余的@符号
  newDisplayExpression = newDisplayExpression.replace(/@/g, '')

  console.log('插入变量后的显示表达式（保持大括号）:', newDisplayExpression)

  console.log('新显示表达式:', newDisplayExpression)

  // 使用processChineseVariables转换为存储格式并保存
  const newStorageExpression = processChineseVariables(newDisplayExpression)
  field.expression = newStorageExpression

  console.log('processChineseVariables转换后的存储表达式:', newStorageExpression)

  // 隐藏下拉框
  hideVariableDropdown(fieldIndex)

  // 强制更新输入框的值
  nextTick(() => {
    // 找到对应的输入框并更新其值
    const textareas = document.querySelectorAll('.expression-textarea')
    const targetTextarea = textareas[fieldIndex] as HTMLTextAreaElement
    if (targetTextarea) {
      // 显示值保持变量的大括号格式，只移除@符号
      const displayValue = convertStorageToDisplay(newStorageExpression)
      targetTextarea.value = displayValue
      console.log('强制更新输入框显示值（保持大括号格式）:', displayValue)

      // 触发input事件，确保Vue响应式更新
      const inputEvent = new Event('input', { bubbles: true })
      targetTextarea.dispatchEvent(inputEvent)
    }

    // 触发变化处理
    createExpressionChangeHandler(fieldIndex)(newStorageExpression)
  })

  console.log('变量插入完成')
}

// 自定义表达式编辑器已完全替代VueExpressionEditor

// 组件挂载后验证单选框默认值
onMounted(() => {
  console.log('EditMediationInformationDialog 组件已挂载，自定义表达式编辑器已启用')

  // 验证单选框默认值
  nextTick(() => {
    console.log('验证动态字段的默认逻辑处理类型:', dynamicFields.value.map(f => ({ id: f.id, logic_type: f.logic_type })))
  })
})

// 关闭弹框
function close() {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.value.creditor = null
  formData.value.field_mappings_detail = []
  previewContent.value = ''
  emit('update:visible', false)
}

// 生成唯一字段ID
function generateFieldId(): string {
  return 'GZTJ' + Math.random().toString(36).substring(2, 11)
}

// 添加新字段
function addField() {
  const newField: ExtendedMediationField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    expression: '',
    preview: '',
    // 新字段默认使用文本格式化
    logic_type: 'text_format',
  }

  // 先添加字段
  dynamicFields.value.push(newField)

  // 强制触发整个数组的响应式更新
  dynamicFields.value = [...dynamicFields.value]

  // 多次确保新字段的默认值正确设置
  nextTick(() => {
    const lastIndex = dynamicFields.value.length - 1
    if (dynamicFields.value[lastIndex]) {
      dynamicFields.value[lastIndex].logic_type = 'text_format'
      console.log('新字段已添加，默认逻辑处理类型:', dynamicFields.value[lastIndex].logic_type)
    }

    // 延迟再次确认
    setTimeout(() => {
      if (dynamicFields.value[lastIndex]) {
        dynamicFields.value[lastIndex].logic_type = 'text_format'
        console.log('延迟确认新字段逻辑处理类型:', dynamicFields.value[lastIndex].logic_type)
      }

      // 自定义表达式编辑器已完全支持中文输入
      console.log('新字段自定义表达式编辑器已启用')
    }, 100)
  })
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 处理逻辑处理类型变化
function handleLogicTypeChange(value: any, fieldIndex: number) {
  const logicType = String(value)
  if (dynamicFields.value[fieldIndex]) {
    // 更新字段的逻辑处理类型
    dynamicFields.value[fieldIndex].logic_type = logicType

    // 如果当前字段有表达式内容，重新解析预览
    const currentExpression = dynamicFields.value[fieldIndex].expression
    if (currentExpression && currentExpression.trim()) {
      // 直接调用parseExpressionContent，因为currentExpression已经是存储格式（{变量名}）
      parseExpressionContent(currentExpression, fieldIndex)
    }
  }
}

/**
 * 触发文件选择器（重新上传）
 */
function triggerFileSelector() {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true // 支持多文件选择
  input.accept = '*' // 不限制文件类型
  input.style.display = 'none'
  
  input.addEventListener('change', handleRelatedFileChange)
  
  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}
/**
 * 相关文件上传处理 - 支持多文件同时选择
 * @param event 文件选择事件
 */
function handleRelatedFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const fileList = target.files
  
  if (!fileList || fileList.length === 0) return
  
  // 遍历所有选中的文件
  Array.from(fileList).forEach(file => {
    const uploadFile: ExtendedUploadFile = {
      id: generateFieldId(),
      file_name: file.name,
      url: URL.createObjectURL(file),
      file: file,
      isServerFile: false
    }

    relatedFiles.value.push(uploadFile)
  })
  
  // 清空input值，允许重复选择相同文件
  target.value = ''
}

// 删除相关文件
function removeRelatedFile(fileIndex: number) {
  const file = relatedFiles.value[fileIndex]
  // 释放创建的URL
  if (file.file && !file.isServerFile) {
    URL.revokeObjectURL(file.url!)
  }
  relatedFiles.value.splice(fileIndex, 1)
}

/**
 * 更新字段映射 - 支持新的数据结构
 * @param index 字段索引
 * @param fieldConfigId 字段配置ID（可以为null）
 */
function updateFieldMapping(index: number, fieldConfigId: any) {
  const configId = fieldConfigId as number | null
  if (formData.value.field_mappings_detail[index]) {
    if (configId) {
      // 找到对应的字段配置
      const fieldConfig = fieldConfigOptions.value.find(config => config.id === configId)
      if (fieldConfig) {
        formData.value.field_mappings_detail[index].mapped_field_config = {
          id: fieldConfig.id,
          field_name: fieldConfig.field_name
        }
      }
    } else {
      // 清空映射
      formData.value.field_mappings_detail[index].mapped_field_config = null
    }
  }
}

// 处理表达式变化事件 - 创建闭包函数来传递fieldIndex
function createExpressionChangeHandler(fieldIndex: number) {
  return async (expression: any) => {
    const expressionStr = String(expression || '')

    console.log('表达式输入变化:', expressionStr, 'fieldIndex:', fieldIndex)

    // 更新字段的表达式内容（保持用户输入的原始格式，包括@符号）
    if (dynamicFields.value[fieldIndex]) {
      dynamicFields.value[fieldIndex].expression = expressionStr
    }

    // 输入过程中不进行复杂处理，保持响应性
    // 实际的变量转换和接口调用在失焦时进行
  }
}

// 处理表达式失焦事件 - 创建闭包函数来传递fieldIndex
function createExpressionBlurHandler(fieldIndex: number) {
  return async (expression: any) => {
    const expressionStr = String(expression)
    console.log("失焦时进行表达式处理和预览 - 接收到的表达式:", expressionStr, "fieldIndex:", fieldIndex)

    if (!expressionStr.trim()) {
      // 清空预览内容
      if (dynamicFields.value[fieldIndex]) {
        dynamicFields.value[fieldIndex].preview = ''
      }
      return
    }

    // 检查是否只是@符号或不完整的表达式
    if (expressionStr.trim() === '@' || expressionStr.includes('@')) {
      console.log("表达式包含@符号，跳过接口调用:", expressionStr)
      // 清空预览内容
      if (dynamicFields.value[fieldIndex]) {
        dynamicFields.value[fieldIndex].preview = ''
      }
      return
    }

    // 验证表达式格式 - 确保变量都是{变量名}格式
    if (!validateExpressionFormat(expressionStr)) {
      console.log("表达式格式不正确，变量未用大括号包裹，跳过接口调用:", expressionStr)
      // 清空预览内容
      if (dynamicFields.value[fieldIndex]) {
        dynamicFields.value[fieldIndex].preview = ''
      }
      return
    }

    // 失焦时进行表达式处理和预览
    // expressionStr应该是存储格式（带大括号）
    await parseExpressionContent(expressionStr, fieldIndex)
  }
}

// 兼容性函数 - 保持原有函数名
async function handleExpressionChange(expression: any, fieldIndex?: number) {
  if (fieldIndex === undefined) {
    console.warn('handleExpressionChange called without fieldIndex')
    return
  }
  return createExpressionChangeHandler(fieldIndex)(expression)
}

async function handleExpressionBlur(expression: any, fieldIndex?: number) {
  if (fieldIndex === undefined) {
    console.warn('handleExpressionBlur called without fieldIndex')
    return
  }
  return createExpressionBlurHandler(fieldIndex)(expression)
}

// 反向转换：将大括号格式转换为用户友好的显示格式，保持变量的大括号包裹
function convertBracesToDisplayFormat(expression: string): string {
  if (!expression || !expression.trim()) {
    return expression
  }

  // 只移除@符号，保持{变量名}格式用于视觉区分
  let result = expression.replace(/@/g, '')

  console.log('显示转换（保持大括号）:', expression, '->', result)
  return result
}

// 处理变量表达式，转换为大括号格式
function processChineseVariables(expression: string): string {
  // 处理三种情况：
  // 1. @符号开头的变量：@合同号 -> {合同号}
  // 2. 直接输入的中文变量名：合同号 -> {合同号}（仅当该变量名在变量列表中存在时）
  // 3. 已经被大括号包裹的变量：{合同号} -> {合同号}（保持不变）
  // 4. 用户自己输入的非变量中文：保持原样，不用大括号包裹
  // 其他普通文本、数字、运算符保持原样

  if (!expression || !expression.trim()) {
    return expression
  }

  let processedExpression = expression.trim()

  // 获取当前可用的变量名列表
  const availableVariableNames = expressionVariables.value.map(variable => variable.name)

  if (availableVariableNames.length === 0) {
    console.log('没有可用的变量列表')
    return processedExpression
  }

  // 1. 处理@符号开头的变量名（支持中文、英文、数字组合）
  // 更精确的正则表达式，匹配@后面的完整变量名
  processedExpression = processedExpression.replace(/@([^\s+\-*/(){}@,，。！？；：""'']+)/g, (fullMatch, variableName) => {
    // 移除@符号，用大括号包裹变量名
    const cleanVariableName = variableName.trim()
    console.log(`处理@变量: ${fullMatch} -> {${cleanVariableName}}`)
    return `{${cleanVariableName}}`
  })

  // 2. 处理直接输入的变量名（按变量名长度从长到短排序，避免短变量名覆盖长变量名）
  const sortedVariableNames = [...availableVariableNames].sort((a, b) => b.length - a.length)

  sortedVariableNames.forEach(variableName => {
    if (!variableName || variableName.trim() === '') return

    // 转义特殊字符，创建精确匹配的正则表达式
    const escapedVariableName = variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    // 创建更简单的正则表达式，匹配独立的变量名
    // 使用单词边界 \b 确保完整匹配，但对中文字符需要特殊处理
    let regex: RegExp
    if (/[\u4e00-\u9fa5]/.test(variableName)) {
      // 中文变量名，使用更宽松的边界匹配
      regex = new RegExp(`(?<!\\{)${escapedVariableName}(?!\\})`, 'g')
    } else {
      // 英文变量名，使用单词边界
      regex = new RegExp(`(?<!\\{)\\b${escapedVariableName}\\b(?!\\})`, 'g')
    }

    // 替换所有匹配的变量名
    let lastProcessed = ''
    while (lastProcessed !== processedExpression) {
      lastProcessed = processedExpression
      processedExpression = processedExpression.replace(regex, (match, offset) => {
        // 检查这个位置是否已经在大括号内
        const beforeMatch = processedExpression.substring(0, offset)

        // 计算大括号的平衡
        const openBraces = (beforeMatch.match(/\{/g) || []).length
        const closeBraces = (beforeMatch.match(/\}/g) || []).length

        // 如果大括号不平衡，说明当前在大括号内，不处理
        if (openBraces > closeBraces) {
          return match
        }

        console.log(`处理直接变量: ${match} -> {${match}}`)
        return `{${match}}`
      })
    }
  })

  // 3. 清理可能的重复大括号（如 {{变量名}} -> {变量名}）
  processedExpression = processedExpression.replace(/\{\{([^}]+)\}\}/g, '{$1}')

  console.log('原始表达式:', expression)
  console.log('可用变量列表:', availableVariableNames)
  console.log('转换后的表达式:', processedExpression)

  return processedExpression
}

// VueExpressionEditor相关函数（简化版）

// 插入变量到表达式中
function insertVariable(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return

  const currentExpression = dynamicFields.value[fieldIndex].expression || ''
  const variableName = variable.name

  // 在当前表达式末尾添加变量名（不使用@符号，直接添加变量名）
  const newExpression = currentExpression ? `${currentExpression}+${variableName}` : variableName

  // 更新字段表达式
  dynamicFields.value[fieldIndex].expression = newExpression

  console.log(`插入变量: ${variableName}, 新表达式: ${newExpression}`)
}

// 插入变量到表达式中（用于变量按钮点击）
function insertVariableToExpression(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return

  const currentExpression = dynamicFields.value[fieldIndex].expression || ''
  const variableName = `@${variable.name}`

  // 在当前表达式末尾添加变量
  const newExpression = currentExpression ? `${currentExpression} + ${variableName}` : variableName

  dynamicFields.value[fieldIndex].expression = newExpression

  console.log('插入变量:', variableName, '新表达式:', newExpression)

  // 触发表达式变化处理，el-input原生支持中文输入
  console.log('插入变量:', variableName, '新表达式:', newExpression)
}



// 处理VueExpressionEditor的中文输入问题
function handleChineseInput(event: any, fieldIndex: number) {
  // 如果组件阻止了中文输入，我们通过这个函数来处理
  const inputValue = event.target?.value || event
  console.log('中文输入处理:', inputValue, 'fieldIndex:', fieldIndex)

  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].expression = inputValue
  }
}

// 解析表达式内容
async function parseExpressionContent(expression: string, fieldIndex: number) {
  // 获取当前字段的逻辑处理类型
  const currentField = dynamicFields.value[fieldIndex]
  const selectedLogicType = currentField?.logic_type || 'text_format'
  // 调用真实的后台接口，使用新的参数格式
  const {data} = await expressionCalculation({
    asset_package_id: props.rowData?.id ? Number(props.rowData.id) : undefined,
    // asset_package_row_number: null,
    expression: expression, // 经过processChineseVariables处理的大括号格式表达式
    logic_type: selectedLogicType // 用户选中的逻辑处理类型值
  })
  
  // 将接口返回的response.data.data值赋值给内容预览preview
  const previewResult = data.data
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].preview = String(previewResult)
  }
}
// 确认编辑
async function ensureEdit() {
  if (!formRef.value) return

  loading.value = true
  try {
    const submitFormData = new FormData()
    
    // 根据编辑模式添加不同的字段
    if (props.editMode === 'mediation') {
      // 调解案件模式：可编辑债务人
      submitFormData.append('debtor', formData.value.debtor)
    }
    
    // props.editMode === 'asset'添加调解信息配置（两种模式都可能需要）
    if (props.editMode === 'asset' && editableFields.value.includes('field_mappings_detail')) {
      
      // 处理动态字段数据，确保包含表达式信息和逻辑处理类型
      // field.expression如果是变量则改为{变量}+数字+{变量}
      const processedFields = dynamicFields.value.map(field => {
        // 判断expression和title不能为空
        if (!field.expression.trim()) {
          ElMessage.error('表达式编辑不能为空')
          return
        }
        if (!field.title.trim()) {
          ElMessage.error('字段标题不能为空')
          return
        }
        const processedExpression = processChineseVariables(field.expression || '')
        return {
          ...field,
          expression: processedExpression,
          logic_type: field.logic_type || 'text_format' // 确保包含逻辑处理类型
        }
      })
      /* const processedFields = dynamicFields.value.map(field => ({
        id: field.id,
        title: field.title,
        type: field.type,
        value: field.value,
        expression: field.expression || '',
        preview: field.preview || ''
      })) */
      console.log(processedFields,'=====配置')
      submitFormData.append('mediation_config', JSON.stringify(processedFields))
    }

    // 处理文件上传
    if (editableFields.value.includes('attachments')) {
      relatedFiles.value.forEach((fileItem) => {
        if (fileItem.file && !fileItem.isServerFile) {
          // 新上传的文件
          submitFormData.append('file', fileItem.file)
        } else if (fileItem.isServerFile && fileItem.id) {
          // 保留的服务器文件
          submitFormData.append('file_id', fileItem.id)
        }
      })
    }else{
      relatedFiles.value.forEach((fileItem) => {
        if (fileItem.file && !fileItem.isServerFile) {
          submitFormData.append('file', fileItem.file)
        }
      })
    }

    emit('confirm', submitFormData)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog :visible="visible" @update:visible="close" width="1000px" :title="dialogTitle" class="edit-mediation-information-dialog">
    <div class="edit-dialog-content" v-loading="detailLoading" element-loading-text="正在加载详情数据...">
      <el-form ref="formRef" :model="formData" label-width="110px">
        <el-form-item label="资产包名称" prop="asset_package_name">
          <el-input
            v-model="formData.asset_package_name"
            disabled />
        </el-form-item>
        <el-form-item label="债权人" prop="creditor_name">
          <el-input
            v-model="formData.creditor_name"
            disabled />
        </el-form-item>
        <el-form-item 
          v-if="editMode === 'mediation'" 
          label="债务人" 
          prop="debtor">
          <el-select
            v-model="formData.debtor"
            filterable
            placeholder="请选择债务人"
            style="width: 100%"
            clearable>
            <el-option
              v-for="option in debtorOptions"
              :key="option.value + 'zwr'"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="editableFields.includes('attachments')" class="file-section">
         <div class="section-header">
           <label class="field-label">相关文件</label>
           <CustomButton @click="triggerFileSelector" :height="32" style="margin-left: -3px;">
            <i class="jt-20-upload"></i>选择文件
          </CustomButton>
         </div>
         
         <!-- 已上传文件列表 -->
         <div v-if="relatedFiles.length > 0" class="file-list">
           <div
             v-for="(file, fileIndex) in relatedFiles"
             :key="(file.id || 'file') + fileIndex"
             class="file-item">
             <div class="file-info">
               <span class="file-name" :title="file.file_name">{{ file.file_name }}</span>
               <!-- <span class="file-type" v-if="file.isServerFile">[服务器文件]</span>
               <span class="file-type" v-else>[新上传]</span> -->
             </div>
             <div class="file-actions">
               <i
                 class="jt-20-wrong file-action file-remove"
                 @click="removeRelatedFile(fileIndex)"
                 title="删除文件"></i>
             </div>
           </div>
         </div>
       </div>

      <!-- 字段配置信息展示（禁用） - 仅调解案件模式显示 -->
      <div v-if="editMode === 'mediation' && dynamicFields.length > 0" class="config-display-section">
        <div class="section-header">
          <h3>调解信息配置</h3>
        </div>

        <div class="fields-display">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id"
            class="field-display-item">

            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
            </div>

            <div class="field-config">
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  disabled
                  style="width: 280px;">
                  <el-option label="文本类型" :value="FieldTypeEnum.TEXTAREA" />
                  <el-option label="日期类型" :value="FieldTypeEnum.DATE" />
                  <el-option label="金额类型" :value="FieldTypeEnum.AMOUNT" />
                </el-select>
              </div>
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  disabled
                  style="width: 280px;"/>
              </div>
            </div>

            <div class="logic-type-section" :key="`logic-type-${field.id}-${index}`">
              <label class="config-label">逻辑类型：</label>
              <el-radio-group
                v-model="field.logic_type"
                class="logic-type-radio-group"
                @change="(value) => handleLogicTypeChange(value, index)"
                :key="`radio-group-${field.id}-${index}`"
                :disabled="editMode === 'mediation'"
              >
                <el-radio
                  label="text_format"
                  size="small"
                  :key="`text-format-${field.id}-${index}`"
                  :disabled="editMode === 'mediation'"
                >
                  文本格式化
                </el-radio>
                <el-radio
                  label="result_calculation"
                  size="small"
                  :key="`result-calculation-${field.id}-${index}`"
                  :disabled="editMode === 'mediation'"
                >
                  结果运算
                </el-radio>
              </el-radio-group>
            </div>

            <div v-if="field.expression && field.expression.trim()" class="expression-preview">
              <label class="config-label">表达式预览：</label>
              <span class="preview-content" v-html="formatExpressionForDisplay(field.expression)"></span>
            </div>

             <div class="preview-section">
              内容预览：<div class="preview-content" v-loading="previewLoading">{{ field.preview }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 调解信息配置（仅资产包模式可编辑） -->
      <div v-if="editMode === 'asset' && editableFields.includes('field_mappings_detail')" class="config-section">
        <div class="section-header">
          <h3>调解信息配置</h3>
          <CustomButton @click="addField" :height="32" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>

        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id || `field-${index}`"
            class="field-item">
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton
                @click="removeField(index)"
                :height="28"
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>
            <div class="field-config">
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  style="width: 280px;">
                  <el-option label="文本类型" :value="FieldTypeEnum.TEXTAREA" />
                  <el-option label="日期类型" :value="FieldTypeEnum.DATE" />
                  <el-option label="金额类型" :value="FieldTypeEnum.AMOUNT" />
                </el-select>
              </div>
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>
              
            <!-- 逻辑处理类型选择区域 -->
            <div class="logic-type-section" :key="`logic-type-${field.id}-${index}`">
              <label class="config-label">逻辑类型：</label>
              <el-radio-group
                v-model="field.logic_type"
                class="logic-type-radio-group"
                @change="(value) => handleLogicTypeChange(value, index)"
                :key="`radio-group-${field.id}-${index}`"
              >
                <el-radio
                  label="text_format"
                  size="small"
                  :key="`text-format-${field.id}-${index}`"
                >
                  文本格式化
                </el-radio>
                <el-radio
                  label="result_calculation"
                  size="small"
                  :key="`result-calculation-${field.id}-${index}`"
                >
                  结果运算
                </el-radio>
              </el-radio-group>
            </div>

            <div class="expression-section">
              <label class="config-label">表达式编辑：</label>

              <!-- 自定义表达式编辑器 - 完全支持中文输入和运算符 -->
              <div class="custom-expression-editor">
                <div class="expression-input-container">
                  <el-input
                    :model-value="convertStorageToDisplay(field.expression)"
                    @update:model-value="handleExpressionInput($event, index)"
                    type="textarea"
                    :rows="3"
                    :placeholder="getExpressionPlaceholder(field.expression)"
                    @blur="handleExpressionBlurEvent($event, index)"
                    @keydown="handleExpressionKeydown($event, index)"
                    class="expression-textarea"
                    ref="expressionTextarea"
                  />
                  <!-- 变量选择下拉框 -->
                  <div
                    v-if="showVariableDropdown[index]"
                    class="variable-dropdown"
                  >
                    <div class="dropdown-header">
                      <span>选择变量：</span>
                      <el-button size="small" text @click="hideVariableDropdown(index)">×</el-button>
                    </div>

                    <!-- 变量搜索框 -->
                    <div class="variable-search">
                      <el-input
                        v-model="variableSearchText[index]"
                        size="small"
                        placeholder="搜索变量名称..."
                        @input="searchVariables(variableSearchText[index] || '', index)"
                        clearable
                      />
                    </div>

                    <div class="variable-list">
                      <div
                        v-for="variable in filteredVariables[index] || expressionVariables"
                        :key="variable.code"
                        @click.stop="insertVariableAtCursor(variable, index)"
                        @mousedown.prevent
                        class="variable-item"
                      >
                        <span class="variable-name">{{ variable.name }}</span>
                        <!-- <span class="variable-code">{{ variable.code }}</span> -->
                      </div>

                      <!-- 无搜索结果提示 -->
                      <div
                        v-if="(filteredVariables[index] || expressionVariables).length === 0"
                        class="no-results"
                      >
                        未找到匹配的变量
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 表达式预览区域 - 显示格式化的变量 -->
            <div v-if="field.expression && field.expression.trim()" class="expression-preview">
              <span class="preview-label">表达式预览：</span>
              <span class="preview-content" v-html="formatExpressionForDisplay(field.expression)"></span>
            </div>
            <div class="preview-section">
              内容预览：<div class="preview-content" v-loading="previewLoading">{{ field.preview }}</div>
            </div>
          </div>
        </div>

        <div v-else class="empty-fields">
          <p>暂无调解信息配置，请点击"添加字段"开始配置</p>
        </div>
      </div>

      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-dialog-content {
  .file-section {
    margin-bottom: 16px;
    .section-header{
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
      .field-label{
        text-align: right;
        width: 97px;
        font-size: 16px;
        color: #212121;
      }
    }
  }
  .config-section, .config-display-section {

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      line-height: 32px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;

      .field-label, h3 {
        margin: 0;
        font-size: 16px;
        color: #212121;
      }
    }
  }

  // 字段映射样式（参考EditDialog.vue）
  .field-mapping {
    .mapping-container {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      overflow: hidden;

      .mapping-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        background-color: #f5f7fa;

        .field-column,
        .file-column,
        .debtor-column {
          padding: 12px 16px;
          font-weight: 600;
          color: #606266;
          border-right: 1px solid #ebeef5;

          &:last-child {
            border-right: none;
          }
        }
      }

      .mapping-list {
        max-height: 400px;
        overflow-y: auto;

        .mapping-item {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          border-bottom: 1px solid #ebeef5;

          &:last-child {
            border-bottom: none;
          }

          .field-info {
            padding: 16px;
            border-right: 1px solid #ebeef5;
            display: flex;
            flex-direction: column;
            gap: 4px;

            .field-label {
              color: #303133;
              font-weight: 500;
              font-size: 16px;
            }
          }

          .column-select,
          .debtor-select {
            padding: 16px;
            display: flex;
            align-items: center;
            border-right: 1px solid #ebeef5;

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
  }

  .config-display-section {
    .fields-display {
      .field-display-item {
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 20px;
        background: #f9f9f9;

        .field-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .field-index {
            font-weight: bold;
            color: #909399;
            font-size: 16px;
            padding: 4px 12px;
            background-color: #f4f4f5;
            border-radius: 16px;
            border: 1px solid #dcdfe6;
          }
        }

        .field-config {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-bottom: 10px;

          .config-row {
            display: flex;
            align-items: center;
            gap: 5px;
          }

          .config-label {
            min-width: 90px;
            font-size: 16px;
            color: #666;
            font-weight: 500;
          }

          .expression-display {
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            width: 280px;

            .expression-text {
              :deep(.variable-highlight) {
                background: #e3f2fd;
                color: #1976d2;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: 500;
                border: 1px solid #bbdefb;
              }
            }
          }
        }
      }
      .logic-type-section{
        padding-top: 5px;
      }
    }
  }
  .file-upload-component{
    width: 91%;
    height: 38px;
  }
  .file-list {
    margin-left: 110px;
    .file-item {
      margin-top: 12px;
      padding: 8px 12px;
      background-color: #F4F5F7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 175px;

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 8px;
        
        .file-name {
          flex: 1;
          color: #333;
          font-weight: 500;
        }
        
        .file-type {
          font-size: 12px;
          color: #666;
        }
      }

      .file-actions {
        .file-remove {
          color: #f56c6c;
          cursor: pointer;
          padding: 4px;
          border-radius: 50%;
          transition: all 0.2s ease;

         /*  &:hover {
            color: #fff;
            background-color: #f56c6c;
          } */
        }
      }
    }
  }
  
  .fields-list {
    font-size: 16px;
    color: #666;
    font-weight: 500;
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: #fafafa;

      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }

      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 15px;

        .config-row {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .config-label {
          min-width: 90px;
        }
      }

      .expression-section {
        margin-bottom: 22px;
        display: flex;

        .config-label {
          display: block;
          margin-bottom: 8px;
        }
      }
    }
  }
  .expression-preview {
    margin-top: 16px;
    margin-bottom: 18px;
    line-height: 1.4;
    .config-label{
      display: inline-block;
      width: 96px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }
    .preview-content {
      // font-family: 'Courier New', monospace;
      font-size: 16px;
      color: #666;
      font-weight: 500;

      :deep(.variable-highlight) {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: 500;
        border: 1px solid #bbdefb;
      }
    }
  }
  .preview-section {
    display: flex;
    font-size: 16px;
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
    gap: 16px;
  }
  .empty-fields {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #ddd;
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}
:deep(.editor-content){
  display: none;
}
:deep(.formula-editor){
  padding:0
}
:deep(.clear-button){
  font-size: 16px;
  background-color: #D94323;
}
:deep(.formula-input-container .input-tip){
  margin: 2px 13px;
  text-align: left;
}
:deep(.suggestion-item .variable-code){
  display: none;
}

// 逻辑处理类型选择区域样式
.logic-type-section {
  // margin: 12px 0;

  .config-label {
    display: inline-block;
    width: 96px;
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }

  .logic-type-radio-group {
    .el-radio {
      // margin-right: 16px;

      :deep(.el-radio__label) {
        font-size: 16px;
      }

      &.is-checked .el-radio__label {
        color: #409eff;
      }
    }
  }
}

// 变量按钮样式（作为VueExpressionEditor的辅助功能）
.variable-buttons {
  margin-top: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;

  .variable-label {
    font-size: 12px;
    color: #666;
    margin-right: 8px;
    font-weight: 500;
  }

  .variable-btn {
    margin-right: 6px;
    margin-bottom: 4px;
    font-size: 12px;
    height: 24px;
    padding: 0 8px;
  }
}

// VueExpressionEditor样式优化
:deep(.vue-expression-editor) {
  // 主输入框样式
  input, textarea, [contenteditable] {
    ime-mode: active !important;
    -webkit-ime-mode: active !important;
    -moz-ime-mode: active !important;
    -ms-ime-mode: active !important;

    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
    line-height: 1.5 !important;

    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    user-select: text !important;

    -webkit-user-modify: read-write !important;
    -moz-user-modify: read-write !important;
  }

  // 隐藏错误提示和验证信息
  .error-message,
  .validation-error,
  .input-error,
  .chinese-error,
  .validate-error,
  .expression-error {
    display: none !important;
    visibility: hidden !important;
  }

  // 编辑器容器优化
  .editor-container,
  .input-container,
  .expression-editor,
  .expression-input {
    position: relative !important;

    input, textarea, [contenteditable] {
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      min-height: 32px !important;
      width: 100% !important;
      box-sizing: border-box !important;

      &:focus {
        border-color: #409eff !important;
        outline: none !important;
      }
    }
  }
}

// 自定义表达式编辑器样式
.custom-expression-editor {
  position: relative;
  width: 89%;

  .expression-input-container {
    position: relative;

    .expression-textarea {
      :deep(.el-textarea__inner) {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        line-height: 1.5;
        resize: vertical;
        min-height: 80px;
      }
    }

    

    .variable-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      z-index: 2000;
      max-height: 300px;
      overflow-y: auto;
      pointer-events: auto;

      .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #ebeef5;
        background: #f5f7fa;
        font-size: 12px;
        color: #606266;
        font-weight: 500;
      }

      .variable-search {
        padding: 8px 12px;
        border-bottom: 1px solid #ebeef5;
        background: #fafafa;
      }

      .variable-list {
        .variable-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
          transition: background-color 0.2s;
          user-select: none;
          pointer-events: auto;

          &:hover {
            background: #f5f7fa;
            color: #409eff;
          }

          &:active {
            background: #e6f7ff;
          }

          &:last-child {
            border-bottom: none;
          }

          .variable-name {
            font-size: 13px;
            font-weight: 500;
          }

          .variable-code {
            font-size: 11px;
            color: #909399;
            background: #f0f2f5;
            padding: 2px 6px;
            border-radius: 3px;
          }
        }

        .no-results {
          padding: 16px 12px;
          text-align: center;
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .variable-buttons {
    margin-top: 8px;

    .variable-label {
      font-size: 12px;
      color: #606266;
      margin-right: 8px;
      font-weight: 500;
    }

    .variable-btn {
      margin-right: 8px;
      margin-bottom: 4px;
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }
}

// 逻辑处理类型单选框样式
.logic-type-section {
  margin-bottom: 16px;

  .logic-type-radio-group {
    .el-radio {
      margin-right: 16px;

      .el-radio__label {
        font-size: 13px;
      }
    }
  }
}

:deep(.el-input__inner),:deep(.el-textarea__inner) {
  font-size: 16px;
}
</style>

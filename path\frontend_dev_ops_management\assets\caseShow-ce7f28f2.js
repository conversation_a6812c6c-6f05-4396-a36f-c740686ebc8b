/* empty css             */import{C as ce,c as _e,h as me}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 *//* empty css                       *//* empty css                  */import{d as F,r,v as B,o as Y,q as j,w as s,K as L,e as K,g as e,f as o,h as k,E as g,j as H,k as Q,av as N,l as W,N as P,p as R,m as T,A as pe,J as fe,n as G,F as ve,ap as S,aw as ge,ak as I,ax as he,ay as we,az as be,L as ye,M as De,t as q,O as Ce}from"./index-d4ffb1a1.js";import{C as U}from"./CustomButton-777ba9d4.js";/* empty css                     */import{_ as X}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{_ as z}from"./_plugin-vue_export-helper-c27b6911.js";import{D as Ve}from"./DeleteConfirmDialog-5eb37e5a.js";/* empty css                                                                            */const Z=d=>(R("data-v-357f1ff3"),d=d(),T(),d),xe={class:"form-container"},ke={class:"dialog-footer"},Ee=Z(()=>o("i",{class:"jt-20-ensure"},null,-1)),Ye=Z(()=>o("i",{class:"jt-20-delete"},null,-1)),$e=F({__name:"AddCase",props:{showDialog:{type:Boolean}},emits:["close","ensure"],setup(d,{emit:C}){const _=d,m=r(),f=r(!1),a=r({case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}),V={case_name:[{required:!0,message:"请输入案例名称",trigger:"blur"}],case_date:[{required:!0,message:"请选择案例日期",trigger:"change"}],amount:[{required:!0,message:"请输入涉及金额",trigger:"blur"},{pattern:/^\d*(\.\d{1,2})?$/,message:"金额不能包含字母,小数点后两位",trigger:"blur"}],mediation_result:[{required:!0,message:"请选择调解结果",trigger:"change"}],case_details:[{required:!0,message:"请输入案例详情",trigger:"blur"},{min:10,max:1e3,message:"案例详情长度在10到1000个字符",trigger:"blur"}]};B(()=>_.showDialog,p=>{p&&b()});function b(){a.value={case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""},m.value&&m.value.clearValidate()}function x(){C("close")}async function w(){if(m.value){f.value=!0;try{if(!await new Promise(l=>{m.value.validate(t=>{l(t)})}))return;C("ensure",{...a.value})}catch(p){console.error("表单验证失败:",p),g.error("表单验证失败，请检查输入内容")}finally{f.value=!1}}}return(p,l)=>{const t=H,u=Q,y=N,$=W,M=P;return Y(),j(X,{visible:p.showDialog,"onUpdate:visible":x,width:"600px",title:"新增案例信息"},{default:s(()=>[L((Y(),K("div",xe,[e($,{ref_key:"formRef",ref:m,model:a.value,rules:V,"label-width":"100px",class:"case-form"},{default:s(()=>[e(u,{label:"案例名称",prop:"case_name"},{default:s(()=>[e(t,{modelValue:a.value.case_name,"onUpdate:modelValue":l[0]||(l[0]=v=>a.value.case_name=v),placeholder:"请输入案例名称",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(u,{label:"案例日期",prop:"case_date"},{default:s(()=>[e(y,{modelValue:a.value.case_date,"onUpdate:modelValue":l[1]||(l[1]=v=>a.value.case_date=v),type:"date",placeholder:"选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(u,{label:"涉及金额",prop:"amount"},{default:s(()=>[e(t,{modelValue:a.value.amount,"onUpdate:modelValue":l[2]||(l[2]=v=>a.value.amount=v),placeholder:"请输入涉及金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(u,{label:"调解结果",prop:"mediation_result"},{default:s(()=>[e(t,{modelValue:a.value.mediation_result,"onUpdate:modelValue":l[3]||(l[3]=v=>a.value.mediation_result=v),placeholder:"请输入调解结果",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(u,{label:"案例详情",prop:"case_details"},{default:s(()=>[e(t,{modelValue:a.value.case_details,"onUpdate:modelValue":l[4]||(l[4]=v=>a.value.case_details=v),type:"textarea",placeholder:"请详细描述案例的具体情况、处理过程等",rows:5,maxlength:"1000","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",ke,[e(U,{onClick:w,height:34,loading:f.value,"btn-type":"blue"},{default:s(()=>[Ee,k("确认 ")]),_:1},8,["loading"]),e(U,{onClick:x,height:34},{default:s(()=>[Ye,k("取消 ")]),_:1})])])),[[M,f.value]])]),_:1},8,["visible"])}}});const Me=z($e,[["__scopeId","data-v-357f1ff3"]]),ee=d=>(R("data-v-fa4a2b59"),d=d(),T(),d),Ue={class:"form-container"},Se={class:"dialog-footer"},Ie=ee(()=>o("i",{class:"jt-20-ensure"},null,-1)),qe=ee(()=>o("i",{class:"jt-20-delete"},null,-1)),Ae=F({__name:"EditCase",props:{showDialog:{type:Boolean},caseData:{}},emits:["close","ensure"],setup(d,{emit:C}){const _=d,m=r(),f=r(!1),a=r({case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}),V={case_name:[{required:!0,message:"请输入案例名称",trigger:"blur"}],case_date:[{required:!0,message:"请选择案例日期",trigger:"change"}],amount:[{required:!0,message:"请输入涉及金额",trigger:"blur"},{pattern:/^\d*(\.\d{1,2})?$/,message:"金额不能包含字母,小数点后两位",trigger:"blur"}],mediation_result:[{required:!0,message:"请选择调解结果",trigger:"change"}],case_details:[{required:!0,message:"请输入案例详情",trigger:"blur"},{min:10,max:1e3,message:"案例详情长度在10到1000个字符",trigger:"blur"}]};B(()=>_.showDialog,l=>{l&&b()}),B(()=>_.caseData,()=>{_.showDialog&&_.caseData&&b()},{deep:!0});function b(){_.caseData?a.value={..._.caseData,mediation_result:_.caseData.mediation_result}:x(),m.value&&m.value.clearValidate()}function x(){a.value={case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}}function w(){C("close")}async function p(){if(m.value){f.value=!0;try{if(!await new Promise(t=>{m.value.validate(u=>{t(u)})}))return;C("ensure",{...a.value})}catch(l){console.error("表单验证失败:",l),g.error("表单验证失败，请检查输入内容")}finally{f.value=!1}}}return(l,t)=>{const u=H,y=Q,$=N,M=W,v=P;return Y(),j(X,{visible:l.showDialog,"onUpdate:visible":w,width:"600px",title:"编辑案例信息"},{default:s(()=>[L((Y(),K("div",Ue,[e(M,{ref_key:"formRef",ref:m,model:a.value,rules:V,"label-width":"100px",class:"case-form"},{default:s(()=>[e(y,{label:"案例名称",prop:"case_name"},{default:s(()=>[e(u,{modelValue:a.value.case_name,"onUpdate:modelValue":t[0]||(t[0]=h=>a.value.case_name=h),placeholder:"请输入案例名称",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(y,{label:"案例日期",prop:"case_date"},{default:s(()=>[e($,{modelValue:a.value.case_date,"onUpdate:modelValue":t[1]||(t[1]=h=>a.value.case_date=h),type:"date",placeholder:"选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(y,{label:"涉及金额",prop:"amount"},{default:s(()=>[e(u,{modelValue:a.value.amount,"onUpdate:modelValue":t[2]||(t[2]=h=>a.value.amount=h),placeholder:"请输入涉及金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(y,{label:"调解结果",prop:"mediation_result"},{default:s(()=>[e(u,{modelValue:a.value.mediation_result,"onUpdate:modelValue":t[3]||(t[3]=h=>a.value.mediation_result=h),placeholder:"请输入调解结果",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(y,{label:"案例详情",prop:"case_details"},{default:s(()=>[e(u,{modelValue:a.value.case_details,"onUpdate:modelValue":t[4]||(t[4]=h=>a.value.case_details=h),type:"textarea",placeholder:"请详细描述案例的具体情况、处理过程等",rows:5,maxlength:"1000","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",Se,[e(U,{onClick:p,height:34,loading:f.value,"btn-type":"blue"},{default:s(()=>[Ie,k("确认 ")]),_:1},8,["loading"]),e(U,{onClick:w,height:34},{default:s(()=>[qe,k("取消 ")]),_:1})])])),[[v,f.value]])]),_:1},8,["visible"])}}});const Be=z(Ae,[["__scopeId","data-v-fa4a2b59"]]),ae=d=>(R("data-v-07b02f76"),d=d(),T(),d),Fe={class:"case-show-page"},je={class:"search-header"},Le={class:"search-row"},Ke={class:"search-item"},Ne={class:"search-item"},Pe=ae(()=>o("label",null,"案例日期：",-1)),Re={class:"search-item"},Te=ae(()=>o("i",{class:"jt-20-add"},null,-1)),ze={class:"table-container"},Je={class:"case-details-text"},Oe={class:"operation-buttons"},Ge=["onClick"],He=["onClick"],Qe={class:"pagination-wrapper"},A=10,We=F({__name:"caseShow",setup(d){const C=r(0),_=r(1),m=r(""),f=r(""),a=r([]),V=r(!1),b=r(!1),x=r(!1),w=r(!1),p=r(void 0),l=r(null);async function t(){if(!S()){g.error("登录状态已失效，请重新登录");return}x.value=!0;const c={page:_.value,page_size:A,search:m.value,case_date:f.value},{data:n}=await ge(c),{state:E,msg:D}=n;E==="success"?(a.value=n.data.results,C.value=n.data.count):g.error(D),x.value=!1}function u(){I("搜索案例","案例展示"),_.value=1,t()}function y(c){_.value=c,t()}function $(){I("新增案例","案例展示"),b.value=!0}function M(c){I("编辑案例","案例展示"),p.value={...c},V.value=!0}function v(){b.value=!1}function h(){V.value=!1,p.value=void 0}async function te(c){try{if(!S()){g.error("登录状态已失效，请重新登录");return}const n=await he(c);g.success("案例新增成功"),b.value=!1,t()}catch{g.error("新增案例失败，请重试")}}async function le(c){var n;try{if(!S()){g.error("登录状态已失效，请重新登录");return}const E=await we(c,(n=p.value)==null?void 0:n.id);g.success("案例编辑成功"),V.value=!1,p.value=void 0,t()}catch(E){console.error("编辑案例失败:",E),g.error("编辑案例失败，请重试")}}function se(c){if(!S()){g.error("登录状态已失效，请重新登录");return}l.value=c,w.value=!0}async function oe(){if(l.value)try{I("删除案例","案例展示"),await be(l.value.id),g.success("案例删除成功"),t()}catch(c){console.error("删除案例失败:",c),g.error("删除案例失败，请重试")}finally{w.value=!1,l.value=null}}function ne(c){return`¥${c.toLocaleString()}`}return pe(()=>{t()}),(c,n)=>{var J;const E=N,D=Ce,ie=ye,re=De,de=P;return Y(),K(ve,null,[o("div",Fe,[o("div",je,[o("div",Le,[o("div",Ke,[e(ce,{modelValue:m.value,"onUpdate:modelValue":n[0]||(n[0]=i=>m.value=i),placeholder:"搜索案例名称、案例详情",onKeyup:fe(u,["enter"]),onClick:u},null,8,["modelValue","onKeyup"])]),o("div",Ne,[Pe,e(E,{modelValue:f.value,"onUpdate:modelValue":n[1]||(n[1]=i=>f.value=i),type:"date",placeholder:"请选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:u},null,8,["modelValue"])]),o("div",Re,[e(U,{onClick:$,height:34},{default:s(()=>[Te,k("新增案例 ")]),_:1})])])]),o("div",ze,[L((Y(),j(ie,{data:a.value,border:"",style:{width:"100%"},"cell-style":G(_e),"header-cell-style":G(me)},{default:s(()=>[e(D,{type:"index",label:"序号",width:"80",align:"center"},{default:s(({$index:i})=>[k(q(A*(_.value-1)+i+1),1)]),_:1}),e(D,{align:"center",prop:"case_name",label:"案例名称","min-width":"200"}),e(D,{align:"center",prop:"case_date",label:"案例日期",width:"120"}),e(D,{label:"涉及金额",width:"140",align:"center"},{default:s(({row:i})=>[k(q(ne(i.amount)),1)]),_:1}),e(D,{label:"调解结果",width:"320",align:"center",prop:"mediation_result"},{default:s(({row:i})=>[k(q(i.mediation_result),1)]),_:1}),e(D,{align:"center",prop:"case_details",label:"案例详情","min-width":"500"},{default:s(({row:i})=>[o("div",Je,q(i.case_details),1)]),_:1}),e(D,{label:"操作",width:"180",align:"center",fixed:"right"},{default:s(({row:i,$index:O})=>[o("div",Oe,[o("div",{onClick:ue=>M(i,O),class:"operation-btn edit-btn"},"编辑",8,Ge),o("div",{onClick:ue=>se(i,O),class:"operation-btn delete-btn"},"删除",8,He)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[de,x.value]]),o("div",Qe,[e(re,{background:"",layout:"prev, pager, next",total:C.value,"current-page":_.value,"page-size":A,onCurrentChange:y},null,8,["total","current-page"])])])]),e(Me,{"show-dialog":b.value,onClose:v,onEnsure:te},null,8,["show-dialog"]),e(Be,{"show-dialog":V.value,"case-data":p.value,onClose:h,onEnsure:le},null,8,["show-dialog","case-data"]),e(Ve,{visible:w.value,title:"删除案例",message:`确定要删除案例「${(J=l.value)==null?void 0:J.case_name}」吗？此操作不可撤销。`,"confirm-text":"确认","cancel-text":"取消","onUpdate:visible":n[2]||(n[2]=i=>w.value=i),onConfirm:oe,onCancel:n[3]||(n[3]=i=>w.value=!1)},null,8,["visible","message"])],64)}}});const ca=z(We,[["__scopeId","data-v-07b02f76"]]);export{ca as default};

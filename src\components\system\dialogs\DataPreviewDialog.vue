<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import type {
  PreviewData,
  PreviewDataType,
  DynamicColumn
} from '../type'
import { PreviewDataType as PreviewTypeEnum } from '../type'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import {previewRawData,previewMappedData} from '@/axios/system'

// 组件属性定义
interface Props {
  visible: boolean              // 弹框显示状态
  previewType: PreviewDataType  // 预览类型：运营数据或原始数据
  packageId: number | null      // 资产包ID (package_id)
}

// 组件事件定义
interface Emits {
  (e: 'close'): void           // 关闭弹框
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const previewData = ref<PreviewData | null>(null)

// 计算弹框标题
const dialogTitle = computed(() => {
  if (props.previewType === PreviewTypeEnum.OPERATION) {
    return '运营数据预览'
  } else {
    return '原始数据预览'
  }
})

// 计算表头样式
/* const headerCellStyle = {
  background: '#f5f7fa',
  color: '#606266',
  textAlign: 'center' as const,
  fontWeight: 600
} */

// 计算表格高度
const tableHeight = computed(() => {
  return 'calc(95vh - 200px)'
})

// 监听弹框显示状态，加载数据
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.packageId) {
    loadPreviewData()
  }
})

/**
 * 加载预览数据
 */
async function loadPreviewData() {
  if (!props.packageId) return
  
  loading.value = true
  try {
    // 根据预览类型调用不同的API接口
    let response: any
    if (props.previewType === PreviewTypeEnum.OPERATION) {
      // 调用运营数据预览接口
      response = await previewMappedData(props.packageId)
    } else {
      // 调用原始数据预览接口
      response = await previewRawData(props.packageId)
    }

    // 检查响应状态
    if (response.data.code === 200) {
      previewData.value = response.data.data
    } else {
      ElMessage.error(response.data.msg || '数据加载失败')
      return
    }

  } catch (error: any) {
    console.error('预览数据加载失败:', error)
    ElMessage.error(error?.response?.data?.msg || '预览数据加载失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭弹框
 */
function handleClose() {
  previewData.value = null
  emit('close')
}

/**
 * 格式化单元格内容显示
 * @param row 行数据
 * @param column 列配置
 * @returns 格式化后的内容
 */
function formatCellContent(row: Record<string, any>, column: DynamicColumn): string {
  const value = row[column.key]
  if (value === null || value === undefined || value === '') {
    return '-'
  }
  
  switch (column.type) {
    case 'currency':
      return typeof value === 'string' ? value : `¥${value.toLocaleString()}`
    case 'number':
      return typeof value === 'string' ? value : value.toLocaleString()
    case 'date':
      return value
    case 'text':
    default:
      return String(value)
  }
}

/**
 * 获取列的对齐方式样式
 * @param align 对齐方式
 * @returns 样式对象
 */
function getColumnAlignStyle(align?: 'left' | 'center' | 'right') {
  return {
    textAlign: align || 'left'
  }
}


</script>

<template>
  <CustomDialog
    :visible="visible"
    :title="dialogTitle"
    width="90%"
    @update:visible="handleClose">
    
    <div class="preview-content" v-loading="loading">
      <template v-if="previewData">
        <div class="data-info">
          <div class="info-item">
            <span class="label">资产包名称：</span>
            <span class="value">{{ previewData.package_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">文件名称：</span>
            <span class="value">{{ previewData.file_name }}</span>
          </div>
          <!-- <div class="info-item">
            <span class="label">记录总数：</span>
            <span class="value">{{ previewData.total }} 条</span>
          </div> -->
          <!-- <div class="actions">
            <CustomButton @click="exportData" :height="32">
              <i class="jt-20-upload"></i>导出数据
            </CustomButton>
          </div> -->
        </div>

        <div class="table-wrapper">
          <el-table
            :data="previewData.data"
            border
            stripe
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
            :max-height="tableHeight"
            style="width: 100%">
            <el-table-column
              v-for="column in previewData.columns"
              :key="column.key"
              :prop="column.key"
              :label="column.label"
              :width="column.width"
              :min-width="column.width || 120"
              :align="column.align || 'left'">
              
              <template #default="{ row }">
                <div :style="getColumnAlignStyle(column.align)">
                  {{ formatCellContent(row, column) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
         
        <!-- <div class="dialog-footer">
          <CustomButton @click="handleClose" :height="34">
            <i class="jt-20-delete"></i>关闭
          </CustomButton>
        </div> -->
      </template>
      <div v-else-if="!loading" class="no-data">
        <img src="@/assets/images/icon/construct.svg" alt="无数据">
        <div>暂无预览数据</div>
      </div>
    </div>

  </CustomDialog>
</template>

<style lang="scss" scoped>
.preview-content {
  min-height: 400px;
  
  // 数据信息区域
  .data-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    // margin-bottom: 16px;
    // border-bottom: 1px solid #e6e6e6;
    
    
    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      
      .label {
        color: #606266;
        font-weight: 500;
      }
      
      .value {
        color: #303133;
      }
    }
    
    .actions {
      display: flex;
      gap: 12px;
    }
  }
  
  // 表格包装器
  .table-wrapper {
    border-radius: 8px;
    overflow: hidden;
    
    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #f5f7fa;
            color: #606266;
            font-weight: 600;
            border-bottom: 1px solid #dcdfe6;
          }
        }
      }
      
      .el-table__body-wrapper {
        .el-table__row {
          &:nth-child(even) {
            background-color: #fafafa;
          }
          
          &:hover {
            background-color: #f0f9ff;
          }
          
          .el-table__cell {
            padding: 8px 0;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
  
  // 无数据状态
  .no-data {
    text-align: center;
    padding: 60px 0;
    color: #909399;
    
    img {
      width: 64px;
      height: 64px;
      opacity: 0.5;
      margin-bottom: 16px;
    }
    
    div {
      font-size: 14px;
    }
  }
}

// 底部操作区域
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 0 0 0;
}

// 响应式适配
@media (max-width: 1200px) {
  .preview-content {
    .data-info {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      .actions {
        align-self: flex-end;
      }
    }
  }
}

@media (max-width: 768px) {
  .preview-content {
    .data-info {
      /* .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      } */
      
      .actions {
        align-self: center;
        width: 100%;
        justify-content: center;
      }
    }
    
    .table-wrapper {
      :deep(.el-table) {
        font-size: 12px;
        
        .el-table__cell {
          padding: 6px 4px;
        }
      }
    }
  }
}
</style> 
/* empty css             */import{d as z,u as D,z as F,r as w,b as J,A as P,o as a,e as n,f as r,F as E,B as S,n as R,C as o,D as $,h as j,t as b,G as C,g as B,w as O,H}from"./index-d4ffb1a1.js";import{_ as q}from"./_plugin-vue_export-helper-c27b6911.js";const K={class:"side-bar"},Q={class:"parent-level"},U=["onClick"],W={class:"parent-title-content"},X={key:0},Y={key:0,class:"child-level"},Z=["onClick"],x={class:"child-title-content"},ee={class:"child-title-left"},te={key:0,class:"grandchild-level"},le=["onClick"],se={name:"SidebarComp"},ie=z({...se,props:{background:{},childImage:{},parentImage:{},menuList:{}},emits:["click-parent","click-child","click-grandchild"],setup(T,{emit:ae}){const c=T,f=D(),N=F();let m=w(0),p=w(0),k=w(void 0);const V=JSON.parse(JSON.stringify(c.menuList));let t=J(V);function I(e){if(!t[e].children||t[e].children.length<=0){g(),t[e].isActive=!0;const{link:l}=t[e];f.push(l),m.value=e,p.value=void 0,k.value=void 0}t[e].isCollapse=!t[e].isCollapse}function G(e,l){const s=t[e].children[l];if(s.children&&s.children.length>0){s.isChildCollapse=!s.isChildCollapse;return}g(),t[e].isActive=!0,t[e].isCollapse=!0,t[e].children[l].isChildActive=!0,m.value=e,p.value=l,k.value=void 0,s.link&&f.push(s.link)}function L(e,l,s){g(),t[e].isActive=!0,t[e].isCollapse=!0,t[e].children[l].isChildActive=!0,t[e].children[l].isChildCollapse=!0,t[e].children[l].children[s].isGrandChildActive=!0,m.value=e,p.value=l,k.value=s;const{link:u}=t[e].children[l].children[s];f.push(u)}function g(){t.forEach(e=>{e.isActive=!1,e.children&&e.children.forEach(l=>{l.isChildActive=!1,l.children&&l.children.forEach(s=>{s.isGrandChildActive=!1})})})}return P(()=>{t.forEach((e,l)=>{e.children.forEach((s,u)=>{if(s.link&&s.link.indexOf(N.path)>-1){t[l].isCollapse=!0,G(l,u);return}s.children&&s.children.forEach((d,A)=>{d.link.indexOf(N.path)>-1&&(t[l].isCollapse=!0,s.isChildCollapse=!0,L(l,u,A))})})})}),(e,l)=>{const s=H;return a(),n("div",K,[r("ul",Q,[(a(!0),n(E,null,S(R(t),({isActive:u,isCollapse:d,parent:A,children:h},_)=>(a(),n("li",{key:_,class:o(["parent-list-item"])},[r("div",{class:o(["parent-title",c.parentImage?u?c.parentImage[0]:c.parentImage[1]:u?"default-active-parent":""]),onClick:i=>I(_)},[r("div",W,[$(e.$slots,"custom-parent",{},()=>[j(b(A),1)],!0),h&&h.length>0?(a(),n("span",X,[r("i",{class:o(["arrow-icon",d?"jt-16-arrow-up":"jt-16-arrow-down"])},null,2)])):C("",!0)])],10,U),B(s,null,{default:O(()=>[h&&h.length>0&&d?(a(),n("ul",Y,[(a(!0),n(E,null,S(h,(i,y)=>(a(),n("li",{key:y,class:o(["child-list-item"])},[r("div",{class:o(["child-menu-title",c.childImage?i.isChildActive?c.childImage[0]:c.childImage[1]:i.isChildActive?"default-active-child":""]),onClick:v=>G(_,y)},[r("div",x,[r("div",ee,[r("i",{class:o(i.icon)},null,2),$(e.$slots,"custom-child",{},()=>[j(b(i.name),1)],!0)]),i.children&&i.children.length>0?(a(),n("span",{key:0,class:o(["arrow-icon",i.isChildCollapse?"jt-16-arrow-up":"jt-16-arrow-down"])},null,2)):C("",!0)])],10,Z),B(s,null,{default:O(()=>[i.children&&i.children.length>0&&i.isChildCollapse?(a(),n("ul",te,[(a(!0),n(E,null,S(i.children,(v,M)=>(a(),n("li",{key:M,class:o(["grandchild-list-item",v.isGrandChildActive?"default-active-grandchild":""]),onClick:ne=>L(_,y,M)},[r("i",{class:o(v.icon)},null,2),r("span",null,b(v.name),1)],10,le))),128))])):C("",!0)]),_:2},1024)]))),128))])):C("",!0)]),_:2},1024)]))),128))])])}}});const ue=q(ie,[["__scopeId","data-v-628b02e1"]]);export{ue as S};

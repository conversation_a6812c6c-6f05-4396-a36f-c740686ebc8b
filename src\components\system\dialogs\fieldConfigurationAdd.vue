<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import { getFieldTypes } from '@/axios/system'
import type { 
  FieldConfig, 
  FieldTypeOption,
  ValidationOption
} from '../type'
import { FieldType, ValidationType } from '../type'

// 组件属性定义
interface Props {
  visible: boolean  // 弹框显示状态
}

// 组件事件定义
interface Emits {
  (e: 'close'): void           // 关闭弹框
  (e: 'success'): void         // 新增成功
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据 - 修改：从数组改为单个对象
const loading = ref(false)
const formData = ref<FieldConfig>({
  field_name: '',
  field_type: FieldType.TEXT,
  data_validation: ValidationType.NONE,
  is_masked: false,
  prefix_keep_chars: 0,
  suffix_keep_chars: 0
})

// 字段类型选项和数据校验选项 - 从接口获取
const field_typeOptions = ref<FieldTypeOption[]>([])
const validationOptions = ref<ValidationOption[]>([])

// 动态获取字段类型和数据校验选项下拉框
async function getOptions() {
  try {
    const {data} = await getFieldTypes()
    const { state, msg } = data
    if(state === 'success') {
      // 将对象转换为数组，进行类型转换
      const field_type = Object.entries(data.data.field_types).map(([value, text]) => ({
        label: text as string,
        value: value as FieldType
      }))
      const validation = Object.entries(data.data.validation_types).map(([value, text]) => ({
        label: text as string,
        value: value as ValidationType
      }))
      
      field_typeOptions.value = field_type
      validationOptions.value = validation
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    ElMessage.error('获取字段类型失败')
  }
}

// 监听弹框显示状态，初始化数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData()
    getOptions()
  }
})

/**
 * 初始化表单数据 - 修改：初始化单个表单对象
 */
function initFormData() {
  formData.value = {
    field_name: '',
    field_type: FieldType.TEXT,
    data_validation: ValidationType.NONE,
    is_masked: false,
    prefix_keep_chars: 0,
    suffix_keep_chars: 0
  }
}

/**
 * 确认新增 - 修改：简化验证逻辑，直接验证单个表单对象
 */
async function confirmAdd() {
  // 验证必填字段
  if (!formData.value.field_name.trim()) {
    ElMessage.error('字段名称不能为空')
    return
  }
  // 如果是否脱敏开关为开启，则“前保留字符数和后保留字符数”不能等于0、null
  if(formData.value.is_masked) {
    if(formData.value.prefix_keep_chars === 0 || formData.value.prefix_keep_chars === null) {
      ElMessage.error('前保留字符数不能为0')
      return
    }
    if(formData.value.suffix_keep_chars === 0 || formData.value.suffix_keep_chars === null) {
      ElMessage.error('后保留字符数不能为0')
      return
    }
  }else if(!formData.value.is_masked) {
    // 如果是否脱敏开关为关闭，则“前保留字符数和后保留字符数”清空（赋值0）
    formData.value.prefix_keep_chars = 0
    formData.value.suffix_keep_chars = 0
  }
  emit('success', formData.value)
}

/**
 * 关闭弹框
 */
function handleClose() {
  emit('close')
}
</script>

<template>
  <CustomDialog 
    :visible="visible" 
    @update:visible="handleClose" 
    width="800px" 
    title="新增字段配置">
    <div class="dialog-content" v-loading="loading">
      <el-form 
        :model="formData" 
        label-width="140px"
        label-position="left"
        class="field-config-form">
        
        <el-form-item label="字段名称" required>
          <el-input 
            v-model="formData.field_name" 
            placeholder="请输入字段名称"
            maxlength="50"
            show-word-limit
            style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="字段类型" required>
          <el-select v-model="formData.field_type" placeholder="选择类型" style="width: 100%">
            <el-option
              v-for="option in field_typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据校验" required>
          <el-select v-model="formData.data_validation" placeholder="选择校验" style="width: 100%">
            <el-option
              v-for="option in validationOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否脱敏">
          <el-switch v-model="formData.is_masked" />
        </el-form-item>
        
        <!-- 脱敏配置区域 - 当启用脱敏时显示 -->
        <div v-if="formData.is_masked" class="desensitize-config">
          <el-form-item label="前保留字符数">
            <el-input-number 
              v-model="formData.prefix_keep_chars" 
              :min="0" 
              :max="99"
              placeholder="前保留字符数"
              style="width: 100%" />
          </el-form-item>
          <el-form-item label="后保留字符数">
            <el-input-number 
              v-model="formData.suffix_keep_chars" 
              :min="0" 
              :max="99"
              placeholder="后保留字符数"
              style="width: 100%" />
          </el-form-item>
        </div>
      </el-form>

      <div class="footer-actions">
        <CustomButton @click="confirmAdd" :height="34" :loading="loading" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="handleClose" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.dialog-content {
  // 表单样式 - 新增：替代原有的表格样式
  .field-config-form {
    padding: 24px;
    border-radius: 8px;
  }

  // 底部操作区域
  .footer-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    padding: 12px 0;
  }

  // Element-Plus 组件样式覆盖
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 4px;
    }
  }

  :deep(.el-input-number) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  // 表单项样式优化
  :deep(.el-form-item) {
    margin-bottom: 20px;

    .el-form-item__label {
      color: #606266;
      line-height: 32px;
    }

    .el-form-item__content {
      line-height: 32px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .dialog-content {
    .field-config-form {
      padding: 16px;

      :deep(.el-form-item) {
        .el-form-item__label {
          font-size: 14px;
        }
      }
    }

    .footer-actions {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style>
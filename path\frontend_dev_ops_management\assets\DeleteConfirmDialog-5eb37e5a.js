import{_}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{C as n}from"./CustomButton-777ba9d4.js";import{d as h,c as g,o as v,q as b,w as a,f as t,t as i,g as c,h as d,p as C,m as w}from"./index-d4ffb1a1.js";/* empty css                                                                            */import{_ as B}from"./_plugin-vue_export-helper-c27b6911.js";const l=s=>(C("data-v-58422bc6"),s=s(),w(),s),D={class:"delete-confirm-content"},k=l(()=>t("div",{class:"icon-section"},[t("i",{class:"jt-60-delete delete-icon"})],-1)),T={class:"message-section"},x={class:"delete-message"},y={class:"btns-group"},I=l(()=>t("i",{class:"jt-20-ensure"},null,-1)),S=l(()=>t("i",{class:"jt-20-delete"},null,-1)),V=h({__name:"DeleteConfirmDialog",props:{visible:{type:Boolean},title:{default:"删除确认"},message:{default:"确认删除？此操作不可撤销。"},confirmText:{default:"确认"},cancelText:{default:"取消"},width:{default:"400px"},loading:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(s,{emit:o}){const r=s,p=g({get(){return r.visible},set(e){o("update:visible",e)}});function f(){o("confirm")}function u(){o("cancel"),o("update:visible",!1)}function m(){o("update:visible",!1)}return(e,j)=>(v(),b(_,{title:e.title,visible:p.value,width:e.width,markclose:!0,"onUpdate:visible":m},{default:a(()=>[t("div",D,[k,t("div",T,[t("p",x,i(e.message),1)])]),t("div",y,[c(n,{onClick:f,loading:e.loading,height:34,"btn-type":"red"},{default:a(()=>[I,d(i(e.confirmText),1)]),_:1},8,["loading"]),c(n,{onClick:u,height:34},{default:a(()=>[S,d(i(e.cancelText),1)]),_:1})])]),_:1},8,["title","visible","width"]))}}),A=B(V,[["__scopeId","data-v-58422bc6"]]);export{A as D};

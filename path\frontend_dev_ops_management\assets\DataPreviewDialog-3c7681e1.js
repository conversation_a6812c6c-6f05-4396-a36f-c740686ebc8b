/* empty css             */import{h as B,c as N}from"./headerCellStyle-3128036a.js";import{_ as L}from"./construct-a2f67563.js";import{_ as O}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{P as u}from"./type-bba1e229.js";import{d as A,r as h,c as f,v as V,o as n,q as m,w as _,K as R,e as d,F as g,f as a,t as v,g as F,B as M,aV as $,n as y,G as q,aW as z,aX as G,E as w,O as H,L as K,N as U,p as W,m as X}from"./index-d4ffb1a1.js";import{_ as j}from"./_plugin-vue_export-helper-c27b6911.js";const p=r=>(W("data-v-0d23b6a5"),r=r(),X(),r),J={class:"preview-content"},Q={class:"data-info"},Y={class:"info-item"},Z=p(()=>a("span",{class:"label"},"资产包名称：",-1)),ee={class:"value"},ae={class:"info-item"},te=p(()=>a("span",{class:"label"},"文件名称：",-1)),se={class:"value"},le={class:"table-wrapper"},ie={key:1,class:"no-data"},oe=p(()=>a("img",{src:L,alt:"无数据"},null,-1)),ne=p(()=>a("div",null,"暂无预览数据",-1)),re=[oe,ne],ce=A({__name:"DataPreviewDialog",props:{visible:{type:Boolean},previewType:{},packageId:{}},emits:["close"],setup(r,{emit:b}){const l=r,c=h(!1),i=h(null),k=f(()=>l.previewType===u.OPERATION?"运营数据预览":"原始数据预览"),D=f(()=>"calc(95vh - 200px)");V(()=>l.visible,t=>{t&&l.packageId&&I()});async function I(){var t,o;if(l.packageId){c.value=!0;try{let e;if(l.previewType===u.OPERATION?e=await z(l.packageId):e=await G(l.packageId),e.data.code===200)i.value=e.data.data;else{w.error(e.data.msg||"数据加载失败");return}}catch(e){console.error("预览数据加载失败:",e),w.error(((o=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:o.msg)||"预览数据加载失败")}finally{c.value=!1}}}function S(){i.value=null,b("close")}function x(t,o){const e=t[o.key];if(e==null||e==="")return"-";switch(o.type){case"currency":return typeof e=="string"?e:`¥${e.toLocaleString()}`;case"number":return typeof e=="string"?e:e.toLocaleString();case"date":return e;case"text":default:return String(e)}}function C(t){return{textAlign:t||"left"}}return(t,o)=>{const e=H,T=K,P=U;return n(),m(O,{visible:t.visible,title:k.value,width:"90%","onUpdate:visible":S},{default:_(()=>[R((n(),d("div",J,[i.value?(n(),d(g,{key:0},[a("div",Q,[a("div",Y,[Z,a("span",ee,v(i.value.package_name),1)]),a("div",ae,[te,a("span",se,v(i.value.file_name),1)])]),a("div",le,[F(T,{data:i.value.data,border:"",stripe:"","header-cell-style":y(B),"cell-style":y(N),"max-height":D.value,style:{width:"100%"}},{default:_(()=>[(n(!0),d(g,null,M(i.value.columns,s=>(n(),m(e,{key:s.key,prop:s.key,label:s.label,width:s.width,"min-width":s.width||120,align:s.align||"left"},{default:_(({row:E})=>[a("div",{style:$(C(s.align))},v(x(E,s)),5)]),_:2},1032,["prop","label","width","min-width","align"]))),128))]),_:1},8,["data","header-cell-style","cell-style","max-height"])])],64)):c.value?q("",!0):(n(),d("div",ie,re))])),[[P,c.value]])]),_:1},8,["visible","title"])}}});const me=j(ce,[["__scopeId","data-v-0d23b6a5"]]);export{me as D};

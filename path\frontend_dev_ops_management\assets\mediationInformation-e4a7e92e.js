/* empty css             */import{C as Re,c as be,h as we}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 *//* empty css                       */import{d as Ee,r as m,c as re,v as ue,A as ke,Y as ae,o as n,q as A,w as v,K as oe,e as _,g as i,F as X,B as W,G as D,f as l,h as k,t as L,n as z,Z as Ve,_ as Ne,$ as Be,E as P,a0 as Oe,a1 as je,j as ze,k as Pe,X as He,U as Ke,l as Je,a2 as Xe,a3 as Ge,a4 as We,N as $e,p as qe,m as Ze,I as Ye,J as Qe,a5 as Ie,a6 as et,a7 as tt,M as at,S as lt,O as ot,L as st,a8 as nt}from"./index-d4ffb1a1.js";import{_ as it}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";/* empty css                  *//* empty css                 *//* empty css                     */import{C as le}from"./CustomButton-777ba9d4.js";/* empty css                                                                   */import{F as J}from"./type-bba1e229.js";import{_ as Ce}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                                            */const w=q=>(qe("data-v-d432bc0e"),q=q(),Ze(),q),ct={class:"edit-dialog-content","element-loading-text":"正在加载详情数据..."},rt={key:0,class:"file-section"},ut={class:"section-header"},dt=w(()=>l("label",{class:"field-label"},"相关文件",-1)),pt=w(()=>l("i",{class:"jt-20-upload"},null,-1)),_t={key:0,class:"file-list"},vt={class:"file-info"},mt=["title"],gt={class:"file-actions"},ht=["onClick"],ft={key:1,class:"config-display-section"},yt=w(()=>l("div",{class:"section-header"},[l("h3",null,"调解信息配置")],-1)),bt={class:"fields-display"},wt={class:"field-header"},Vt={class:"field-index"},Et={class:"field-config"},kt={class:"config-row"},$t=w(()=>l("label",{class:"config-label"},"字段类型：",-1)),Ct={class:"config-row"},Mt=w(()=>l("label",{class:"config-label"},"字段标题：",-1)),Tt=w(()=>l("label",{class:"config-label"},"逻辑类型：",-1)),St={key:0,class:"expression-preview"},Ut=w(()=>l("label",{class:"config-label"},"表达式预览：",-1)),Ft=["innerHTML"],At={class:"preview-section"},Dt={class:"preview-content"},Lt={key:2,class:"config-section"},xt={class:"section-header"},Rt=w(()=>l("h3",null,"调解信息配置",-1)),Nt=w(()=>l("i",{class:"jt-20-addition"},null,-1)),Bt={key:0,class:"fields-list"},Ot={class:"field-header"},jt={class:"field-index"},zt=w(()=>l("i",{class:"jt-20-remove"},null,-1)),Pt={class:"field-config"},Ht={class:"config-row"},Kt=w(()=>l("label",{class:"config-label"},"字段类型：",-1)),Jt={class:"config-row"},Xt=w(()=>l("label",{class:"config-label"},"字段标题：",-1)),Gt=w(()=>l("label",{class:"config-label"},"逻辑类型：",-1)),Wt={class:"expression-section"},qt=w(()=>l("label",{class:"config-label"},"表达式编辑：",-1)),Zt={class:"custom-expression-editor"},Yt={class:"expression-input-container"},Qt={key:0,class:"variable-dropdown"},It={class:"dropdown-header"},ea=w(()=>l("span",null,"选择变量：",-1)),ta={class:"variable-search"},aa={class:"variable-list"},la=["onClick"],oa={class:"variable-name"},sa={key:0,class:"no-results"},na={key:0,class:"expression-preview"},ia=w(()=>l("span",{class:"preview-label"},"表达式预览：",-1)),ca=["innerHTML"],ra={class:"preview-section"},ua={class:"preview-content"},da={key:1,class:"empty-fields"},pa=w(()=>l("p",null,'暂无调解信息配置，请点击"添加字段"开始配置',-1)),_a=[pa],va={class:"btns-group"},ma=w(()=>l("i",{class:"jt-20-ensure"},null,-1)),ga=w(()=>l("i",{class:"jt-20-delete"},null,-1)),ha=Ee({__name:"EditMediationInformationDialog",props:{visible:{type:Boolean},rowData:{},editMode:{}},emits:["update:visible","confirm"],setup(q,{emit:$}){const y=q,C=m(),H=m(!1),O=m(!1),V=m({debtor:"",creditor:null,creditor_name:"",asset_package_name:"",field_mappings_detail:[],attachments:[]});m([]);const I=m([]),M=m([]),o=m([]),x=m([]),de=m(""),ee=m(!1),j=m({}),S=m({}),Z=m({}),Y=m({}),se=re(()=>y.editMode==="asset"?"编辑资产包信息":"编辑调解案件信息"),G=re(()=>y.editMode==="asset"?["field_mappings_detail","attachments"]:["debtor","attachments"]);ue(()=>y.visible,t=>{t&&y.rowData&&(ne(),f())},{immediate:!0}),ue(()=>y.rowData,t=>{t&&y.visible&&ne()},{deep:!0});async function ne(){if(!(!y.rowData||!y.rowData.id)){O.value=!0;try{let t=null;if(y.editMode==="asset"){const e=await Ne(Number(y.rowData.id)),{data:a}=e.data;t=a}else{const e=await Be(Number(y.rowData.id)),{data:a}=e.data;t=a}t&&E(t)}catch(t){console.error("加载详情数据失败:",t),P.error("加载详情数据失败，请重试")}finally{O.value=!1}}}function E(t){t&&(console.log("加载编辑数据:",t),V.value.debtor=t.debtor||"",V.value.creditor=t.creditor,V.value.creditor_name=t.creditor_name||"",V.value.asset_package_name=t.asset_package_name||t.package_name||"",M.value=t.attachments?JSON.parse(JSON.stringify(t.attachments)).map(e=>({...e,isServerFile:!0})):[],V.value.field_mappings_detail=t.field_mappings_detail||[],t.mediation_config&&Array.isArray(t.mediation_config)?o.value=t.mediation_config.map(e=>{let a=e.logic_type;return(!a||a===""||a===null||a===void 0)&&(a="text_format"),{id:e.id||_e(),title:e.title||"",type:e.type||J.TEXTAREA,expression:Le(e.expression||""),preview:e.preview||"",logic_type:a}}):o.value=[],ae(()=>{o.value.forEach((e,a)=>{(!e.logic_type||e.logic_type===""||e.logic_type===null||e.logic_type===void 0)&&(e.logic_type="text_format")}),o.value=[...o.value],console.log("数据回显完成，默认逻辑处理类型已设置:",o.value.map(e=>({id:e.id,logic_type:e.logic_type}))),console.log("数据回显完成，自定义表达式编辑器已启用"),setTimeout(()=>{console.log("第一次延迟检查单选框状态:",o.value.map(e=>({id:e.id,logic_type:e.logic_type}))),o.value.forEach((e,a)=>{(!e.logic_type||e.logic_type===""||e.logic_type===null||e.logic_type===void 0)&&(e.logic_type="text_format");const r=e.logic_type;e.logic_type="",ae(()=>{e.logic_type=r||"text_format"})})},100),setTimeout(()=>{console.log("第二次延迟检查单选框状态:",o.value.map(e=>({id:e.id,logic_type:e.logic_type}))),o.value.forEach(e=>{(!e.logic_type||e.logic_type==="")&&(e.logic_type="text_format",console.log("最终修正字段默认值:",e.id,e.logic_type))})},500)}),y.editMode==="asset"&&(x.value=t.mapped_field_names.map(e=>({code:e,name:e}))))}function f(){Oe({page:1,page_size:1e3}).then(t=>{const{state:e,msg:a}=t.data;e==="success"?I.value=t.data.data.results.map(r=>({label:r.debtor_name,value:r.id})):P.error(a)})}function g(t){return t?t.replace(/@/g,""):""}function R(t){if(!t)return!0;for(const e of x.value){const a=e.name,r=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(new RegExp(`(?<!\\{[^}]*)\\b${r}\\b(?![^{]*\\})`,"g").test(t))return console.log("发现未包裹的变量:",a),!1}return!0}function U(t){return!t||t.trim()===""?"请输入表达式，支持中文、数字、运算符（+ - * / =），输入@可选择变量":""}function Q(t){return t?t.replace(/\{([^}]+)\}/g,'<span class="variable-highlight">{$1}</span>'):""}function pe(t,e){console.log("输入事件 - 显示值:",t);const a=te(t);console.log("输入事件 - processChineseVariables转换后的存储值:",a),o.value[e].expression=a,he(e)(a)}function ie(t,e){var F;const r=t.target.value;console.log("失焦事件 - 输入框显示值:",r);const d=((F=o.value[e])==null?void 0:F.expression)||"";console.log("失焦事件 - 当前存储值:",d);const b=g(d);console.log("失焦事件 - 当前显示值:",b);let h=d;r!==b?(console.log("失焦事件 - 检测到手动修改，使用processChineseVariables转换"),h=te(r),console.log("失焦事件 - processChineseVariables转换后的存储值:",h),o.value[e].expression=h):(console.log("失焦事件 - 无手动修改，确保存储值格式正确"),h=te(d),console.log("失焦事件 - processChineseVariables处理后的存储值:",h),o.value[e].expression=h);const N=R(h);console.log("失焦事件 - 表达式格式验证:",N),ce(e),console.log("失焦事件 - 传递给createExpressionBlurHandler的值:",h),De(e)(h)}function p(t,e){const a=t.target;Z.value[e]=a.selectionStart||0,t.key==="@"?(j.value[e]=!0,S.value[e]=x.value,Y.value[e]="",console.log("显示变量选择下拉框")):t.key==="Escape"&&ce(e)}function T(t,e){if(!t||t.trim()==="")S.value[e]=x.value;else{const a=x.value.filter(r=>r.name.toLowerCase().includes(t.toLowerCase())||r.code.toLowerCase().includes(t.toLowerCase()));S.value[e]=a}}function ce(t){j.value[t]=!1,S.value[t]=[]}function Me(t,e){if(!o.value[e])return;console.log("=== 开始插入变量 ==="),console.log("变量信息:",t),console.log("字段索引:",e),console.log("当前字段数据:",o.value[e]);const a=o.value[e],r=g(a.expression||"");console.log("当前显示表达式:",r);const d=Z.value[e]!==void 0?Z.value[e]:r.length;console.log("光标位置:",d);const b=r.substring(0,d),h=r.substring(d);console.log("光标前:",b,"光标后:",h);const N=b.endsWith("@")?b.slice(0,-1):b,F=`{${t.name}}`;let K=N+F+h;K=K.replace(/@/g,""),console.log("插入变量后的显示表达式（保持大括号）:",K),console.log("新显示表达式:",K);const B=te(K);a.expression=B,console.log("processChineseVariables转换后的存储表达式:",B),ce(e),ae(()=>{const c=document.querySelectorAll(".expression-textarea")[e];if(c){const u=g(B);c.value=u,console.log("强制更新输入框显示值（保持大括号格式）:",u);const ye=new Event("input",{bubbles:!0});c.dispatchEvent(ye)}he(e)(B)}),console.log("变量插入完成")}ke(()=>{console.log("EditMediationInformationDialog 组件已挂载，自定义表达式编辑器已启用"),ae(()=>{console.log("验证动态字段的默认逻辑处理类型:",o.value.map(t=>({id:t.id,logic_type:t.logic_type})))})});function me(){C.value&&C.value.resetFields(),V.value.creditor=null,V.value.field_mappings_detail=[],de.value="",$("update:visible",!1)}function _e(){return"GZTJ"+Math.random().toString(36).substring(2,11)}function Te(){const t={id:_e(),title:"",type:J.TEXTAREA,expression:"",preview:"",logic_type:"text_format"};o.value.push(t),o.value=[...o.value],ae(()=>{const e=o.value.length-1;o.value[e]&&(o.value[e].logic_type="text_format",console.log("新字段已添加，默认逻辑处理类型:",o.value[e].logic_type)),setTimeout(()=>{o.value[e]&&(o.value[e].logic_type="text_format",console.log("延迟确认新字段逻辑处理类型:",o.value[e].logic_type)),console.log("新字段自定义表达式编辑器已启用")},100)})}function Se(t){o.value.splice(t,1)}function ge(t,e){const a=String(t);if(o.value[e]){o.value[e].logic_type=a;const r=o.value[e].expression;r&&r.trim()&&fe(r,e)}}function Ue(){const t=document.createElement("input");t.type="file",t.multiple=!0,t.accept="*",t.style.display="none",t.addEventListener("change",Fe),document.body.appendChild(t),t.click(),document.body.removeChild(t)}function Fe(t){const e=t.target,a=e.files;!a||a.length===0||(Array.from(a).forEach(r=>{const d={id:_e(),file_name:r.name,url:URL.createObjectURL(r),file:r,isServerFile:!1};M.value.push(d)}),e.value="")}function Ae(t){const e=M.value[t];e.file&&!e.isServerFile&&URL.revokeObjectURL(e.url),M.value.splice(t,1)}function he(t){return async e=>{const a=String(e||"");console.log("表达式输入变化:",a,"fieldIndex:",t),o.value[t]&&(o.value[t].expression=a)}}function De(t){return async e=>{const a=String(e);if(console.log("失焦时进行表达式处理和预览 - 接收到的表达式:",a,"fieldIndex:",t),!a.trim()){o.value[t]&&(o.value[t].preview="");return}if(a.trim()==="@"||a.includes("@")){console.log("表达式包含@符号，跳过接口调用:",a),o.value[t]&&(o.value[t].preview="");return}if(!R(a)){console.log("表达式格式不正确，变量未用大括号包裹，跳过接口调用:",a),o.value[t]&&(o.value[t].preview="");return}await fe(a,t)}}function Le(t){if(!t||!t.trim())return t;let e=t.replace(/@/g,"");return console.log("显示转换（保持大括号）:",t,"->",e),e}function te(t){if(!t||!t.trim())return t;let e=t.trim();const a=x.value.map(d=>d.name);return a.length===0?(console.log("没有可用的变量列表"),e):(e=e.replace(/@([^\s+\-*/(){}@,，。！？；：""'']+)/g,(d,b)=>{const h=b.trim();return console.log(`处理@变量: ${d} -> {${h}}`),`{${h}}`}),[...a].sort((d,b)=>b.length-d.length).forEach(d=>{if(!d||d.trim()==="")return;const b=d.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");let h;/[\u4e00-\u9fa5]/.test(d)?h=new RegExp(`(?<!\\{)${b}(?!\\})`,"g"):h=new RegExp(`(?<!\\{)\\b${b}\\b(?!\\})`,"g");let N="";for(;N!==e;)N=e,e=e.replace(h,(F,K)=>{const B=e.substring(0,K),s=(B.match(/\{/g)||[]).length,c=(B.match(/\}/g)||[]).length;return s>c?F:(console.log(`处理直接变量: ${F} -> {${F}}`),`{${F}}`)})}),e=e.replace(/\{\{([^}]+)\}\}/g,"{$1}"),console.log("原始表达式:",t),console.log("可用变量列表:",a),console.log("转换后的表达式:",e),e)}async function fe(t,e){var h;const a=o.value[e],r=(a==null?void 0:a.logic_type)||"text_format",{data:d}=await je({asset_package_id:(h=y.rowData)!=null&&h.id?Number(y.rowData.id):void 0,expression:t,logic_type:r}),b=d.data;o.value[e]&&(o.value[e].preview=String(b))}async function xe(){if(C.value){H.value=!0;try{const t=new FormData;if(y.editMode==="mediation"&&t.append("debtor",V.value.debtor),y.editMode==="asset"&&G.value.includes("field_mappings_detail")){const e=o.value.map(a=>{if(!a.expression.trim()){P.error("表达式编辑不能为空");return}if(!a.title.trim()){P.error("字段标题不能为空");return}const r=te(a.expression||"");return{...a,expression:r,logic_type:a.logic_type||"text_format"}});console.log(e,"=====配置"),t.append("mediation_config",JSON.stringify(e))}G.value.includes("attachments")?M.value.forEach(e=>{e.file&&!e.isServerFile?t.append("file",e.file):e.isServerFile&&e.id&&t.append("file_id",e.id)}):M.value.forEach(e=>{e.file&&!e.isServerFile&&t.append("file",e.file)}),$("confirm",t)}finally{H.value=!1}}}return(t,e)=>{const a=ze,r=Pe,d=He,b=Ke,h=Je,N=Xe,F=Ge,K=We,B=$e;return n(),A(it,{visible:t.visible,"onUpdate:visible":me,width:"1000px",title:se.value,class:"edit-mediation-information-dialog"},{default:v(()=>[oe((n(),_("div",ct,[i(h,{ref_key:"formRef",ref:C,model:V.value,"label-width":"110px"},{default:v(()=>[i(r,{label:"资产包名称",prop:"asset_package_name"},{default:v(()=>[i(a,{modelValue:V.value.asset_package_name,"onUpdate:modelValue":e[0]||(e[0]=s=>V.value.asset_package_name=s),disabled:""},null,8,["modelValue"])]),_:1}),i(r,{label:"债权人",prop:"creditor_name"},{default:v(()=>[i(a,{modelValue:V.value.creditor_name,"onUpdate:modelValue":e[1]||(e[1]=s=>V.value.creditor_name=s),disabled:""},null,8,["modelValue"])]),_:1}),t.editMode==="mediation"?(n(),A(r,{key:0,label:"债务人",prop:"debtor"},{default:v(()=>[i(b,{modelValue:V.value.debtor,"onUpdate:modelValue":e[2]||(e[2]=s=>V.value.debtor=s),filterable:"",placeholder:"请选择债务人",style:{width:"100%"},clearable:""},{default:v(()=>[(n(!0),_(X,null,W(I.value,s=>(n(),A(d,{key:s.value+"zwr",label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):D("",!0)]),_:1},8,["model"]),G.value.includes("attachments")?(n(),_("div",rt,[l("div",ut,[dt,i(le,{onClick:Ue,height:32,style:{"margin-left":"-3px"}},{default:v(()=>[pt,k("选择文件 ")]),_:1})]),M.value.length>0?(n(),_("div",_t,[(n(!0),_(X,null,W(M.value,(s,c)=>(n(),_("div",{key:(s.id||"file")+c,class:"file-item"},[l("div",vt,[l("span",{class:"file-name",title:s.file_name},L(s.file_name),9,mt)]),l("div",gt,[l("i",{class:"jt-20-wrong file-action file-remove",onClick:u=>Ae(c),title:"删除文件"},null,8,ht)])]))),128))])):D("",!0)])):D("",!0),t.editMode==="mediation"&&o.value.length>0?(n(),_("div",ft,[yt,l("div",bt,[(n(!0),_(X,null,W(o.value,(s,c)=>(n(),_("div",{key:s.id,class:"field-display-item"},[l("div",wt,[l("span",Vt,"字段 "+L(c+1),1)]),l("div",Et,[l("div",kt,[$t,i(b,{modelValue:s.type,"onUpdate:modelValue":u=>s.type=u,disabled:"",style:{width:"280px"}},{default:v(()=>[i(d,{label:"文本类型",value:z(J).TEXTAREA},null,8,["value"]),i(d,{label:"日期类型",value:z(J).DATE},null,8,["value"]),i(d,{label:"金额类型",value:z(J).AMOUNT},null,8,["value"])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),l("div",Ct,[Mt,i(a,{modelValue:s.title,"onUpdate:modelValue":u=>s.title=u,disabled:"",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])])]),(n(),_("div",{class:"logic-type-section",key:`logic-type-${s.id}-${c}`},[Tt,(n(),A(F,{modelValue:s.logic_type,"onUpdate:modelValue":u=>s.logic_type=u,class:"logic-type-radio-group",onChange:u=>ge(u,c),key:`radio-group-${s.id}-${c}`,disabled:t.editMode==="mediation"},{default:v(()=>[(n(),A(N,{label:"text_format",size:"small",key:`text-format-${s.id}-${c}`,disabled:t.editMode==="mediation"},{default:v(()=>[k(" 文本格式化 ")]),_:2},1032,["disabled"])),(n(),A(N,{label:"result_calculation",size:"small",key:`result-calculation-${s.id}-${c}`,disabled:t.editMode==="mediation"},{default:v(()=>[k(" 结果运算 ")]),_:2},1032,["disabled"]))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange","disabled"]))])),s.expression&&s.expression.trim()?(n(),_("div",St,[Ut,l("span",{class:"preview-content",innerHTML:Q(s.expression)},null,8,Ft)])):D("",!0),l("div",At,[k(" 内容预览："),oe((n(),_("div",Dt,[k(L(s.preview),1)])),[[B,ee.value]])])]))),128))])])):D("",!0),t.editMode==="asset"&&G.value.includes("field_mappings_detail")?(n(),_("div",Lt,[l("div",xt,[Rt,i(le,{onClick:Te,height:32,"btn-type":"blue"},{default:v(()=>[Nt,k("添加字段 ")]),_:1})]),o.value.length>0?(n(),_("div",Bt,[(n(!0),_(X,null,W(o.value,(s,c)=>(n(),_("div",{key:s.id||`field-${c}`,class:"field-item"},[l("div",Ot,[l("span",jt,"字段 "+L(c+1),1),i(le,{onClick:u=>Se(c),height:28,"btn-type":"red"},{default:v(()=>[zt,k("删除 ")]),_:2},1032,["onClick"])]),l("div",Pt,[l("div",Ht,[Kt,i(b,{modelValue:s.type,"onUpdate:modelValue":u=>s.type=u,style:{width:"280px"}},{default:v(()=>[i(d,{label:"文本类型",value:z(J).TEXTAREA},null,8,["value"]),i(d,{label:"日期类型",value:z(J).DATE},null,8,["value"]),i(d,{label:"金额类型",value:z(J).AMOUNT},null,8,["value"])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),l("div",Jt,[Xt,i(a,{modelValue:s.title,"onUpdate:modelValue":u=>s.title=u,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])])]),(n(),_("div",{class:"logic-type-section",key:`logic-type-${s.id}-${c}`},[Gt,(n(),A(F,{modelValue:s.logic_type,"onUpdate:modelValue":u=>s.logic_type=u,class:"logic-type-radio-group",onChange:u=>ge(u,c),key:`radio-group-${s.id}-${c}`},{default:v(()=>[(n(),A(N,{label:"text_format",size:"small",key:`text-format-${s.id}-${c}`},{default:v(()=>[k(" 文本格式化 ")]),_:2},1024)),(n(),A(N,{label:"result_calculation",size:"small",key:`result-calculation-${s.id}-${c}`},{default:v(()=>[k(" 结果运算 ")]),_:2},1024))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]))])),l("div",Wt,[qt,l("div",Zt,[l("div",Yt,[i(a,{"model-value":g(s.expression),"onUpdate:modelValue":u=>pe(u,c),type:"textarea",rows:3,placeholder:U(s.expression),onBlur:u=>ie(u,c),onKeydown:u=>p(u,c),class:"expression-textarea",ref_for:!0,ref:"expressionTextarea"},null,8,["model-value","onUpdate:modelValue","placeholder","onBlur","onKeydown"]),j.value[c]?(n(),_("div",Qt,[l("div",It,[ea,i(K,{size:"small",text:"",onClick:u=>ce(c)},{default:v(()=>[k("×")]),_:2},1032,["onClick"])]),l("div",ta,[i(a,{modelValue:Y.value[c],"onUpdate:modelValue":u=>Y.value[c]=u,size:"small",placeholder:"搜索变量名称...",onInput:u=>T(Y.value[c]||"",c),clearable:""},null,8,["modelValue","onUpdate:modelValue","onInput"])]),l("div",aa,[(n(!0),_(X,null,W(S.value[c]||x.value,u=>(n(),_("div",{key:u.code,onClick:Ve(ye=>Me(u,c),["stop"]),onMousedown:e[3]||(e[3]=Ve(()=>{},["prevent"])),class:"variable-item"},[l("span",oa,L(u.name),1)],40,la))),128)),(S.value[c]||x.value).length===0?(n(),_("div",sa," 未找到匹配的变量 ")):D("",!0)])])):D("",!0)])])]),s.expression&&s.expression.trim()?(n(),_("div",na,[ia,l("span",{class:"preview-content",innerHTML:Q(s.expression)},null,8,ca)])):D("",!0),l("div",ra,[k(" 内容预览："),oe((n(),_("div",ua,[k(L(s.preview),1)])),[[B,ee.value]])])]))),128))])):(n(),_("div",da,_a))])):D("",!0),l("div",va,[i(le,{onClick:xe,loading:H.value,height:34,"btn-type":"blue"},{default:v(()=>[ma,k("确认 ")]),_:1},8,["loading"]),i(le,{onClick:me,height:34},{default:v(()=>[ga,k("取消 ")]),_:1})])])),[[B,O.value]])]),_:1},8,["visible","title"])}}});const fa=Ce(ha,[["__scopeId","data-v-d432bc0e"]]),ya={class:"mediation-management"},ba={class:"search-header"},wa={class:"search-row"},Va={class:"search-item"},Ea={class:"fields-preview"},ka={class:"field-types"},$a=["title"],Ca={key:0,class:"more-fields"},Ma={style:{"white-space":"pre-wrap"}},Ta={class:"operation-buttons"},Sa=["onClick"],Ua={class:"fields-preview"},Fa={class:"field-types"},Aa=["title"],Da={key:0,class:"more-fields"},La={style:{"white-space":"pre-wrap"}},xa={class:"operation-buttons"},Ra=["onClick"],Na={class:"pagination-wrapper"},ve=10,Ba=Ee({__name:"mediationInformation",setup(q){const $=m("asset");m({id:"",asset_package_name:"",mediation_config:[]});const y=m(0),C=m(1),H=m(""),O=m(!1),V=m([]),I=m([]);m(!1),m(!1);const M=m(!1),o=m(null),x=m("asset"),de=re(()=>$.value==="asset"?"资产包名称":"调解案件号"),ee=re(()=>$.value==="asset"?V.value:I.value);ue($,E=>{C.value=1,E==="asset"?j():S()}),ue(H,()=>{C.value=1,$.value==="asset"?j():S()}),ke(()=>{j()});async function j(){O.value=!0;const E={page:C.value,page_size:ve,search:H.value,package_status:"available"},f=await Ye(E),{state:g,msg:R,data:U}=f.data;g==="success"?(V.value=U.results||[],y.value=U.count||0):P.error(R||"获取资产包列表失败"),O.value=!1}async function S(){O.value=!0;const E={page:C.value,page_size:ve,search:H.value},{data:f}=await lt(E),{state:g,msg:R}=f;if(g==="success"){const{results:U,count:Q}=f.data;I.value=U,y.value=Q}else P.error(R);O.value=!1}function Z(){C.value=1,$.value==="asset"?j():S()}function Y(E){C.value=E,$.value==="asset"?j():S()}function se(E,f){o.value=E,x.value=f,M.value=!0}function G(){M.value=!1,o.value=null}async function ne(E){if(!o.value||!o.value.id){P.error("缺少必要参数");return}try{let f;$.value==="asset"?f=await Ie(E,Number(o.value.id)):f=await et(E,Number(o.value.id));const{data:g}=f,{state:R,msg:U}=g;R==="success"?(P.success(U||"编辑成功"),G(),$.value==="asset"?j():S()):P.error(U||"编辑失败")}catch(f){console.error("编辑失败:",f),P.error("编辑失败，请重试")}}return(E,f)=>{const g=ot,R=st,U=nt,Q=tt,pe=at,ie=$e;return n(),_(X,null,[l("div",ya,[l("div",ba,[l("div",wa,[l("div",Va,[i(Re,{modelValue:H.value,"onUpdate:modelValue":f[0]||(f[0]=p=>H.value=p),placeholder:de.value,onKeydown:Qe(Z,["enter"]),onClick:Z},null,8,["modelValue","placeholder","onKeydown"])])])]),i(Q,{modelValue:$.value,"onUpdate:modelValue":f[1]||(f[1]=p=>$.value=p),class:"mediation-tabs"},{default:v(()=>[i(U,{label:"资产包",name:"asset"},{default:v(()=>[oe((n(),A(R,{data:ee.value,border:"","cell-style":z(be),"header-cell-style":z(we),class:"plan-table"},{default:v(()=>[i(g,{label:"序号",type:"index",width:"80",align:"center"}),i(g,{label:"资产包名称",prop:"package_name","min-width":"150",align:"center"}),i(g,{label:"债权人",prop:"creditor_name","min-width":"120",align:"center"}),i(g,{label:"调解信息配置",align:"center","min-width":"220"},{default:v(({row:p})=>[l("div",Ea,[l("div",ka,[(n(!0),_(X,null,W(p.mediation_config&&Array.isArray(p.mediation_config)?p.mediation_config.slice(0,4):[],T=>(n(),_("span",{key:T.id||T.title||Math.random(),class:"field-type-tag",title:T.title},L(T.title),9,$a))),128)),p.mediation_config&&Array.isArray(p.mediation_config)&&p.mediation_config.length>4?(n(),_("span",Ca," +"+L(p.mediation_config.length-4),1)):D("",!0)])])]),_:1}),i(g,{prop:"file_cn",label:"相关文件",align:"left","header-align":"center","min-width":"140"},{default:v(({row:p})=>[l("div",Ma,L(p.file_cn),1)]),_:1}),i(g,{label:"操作",width:"120",align:"center"},{default:v(({row:p})=>[l("div",Ta,[l("div",{onClick:T=>se(p,"asset"),class:"operation-btn edit-btn"},"编辑",8,Sa)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ie,O.value]])]),_:1}),i(U,{label:"调解案件",name:"mediation"},{default:v(()=>[oe((n(),A(R,{data:ee.value,border:"","cell-style":z(be),"header-cell-style":z(we),class:"plan-table"},{default:v(()=>[i(g,{label:"序号",type:"index",width:"80",align:"center"}),i(g,{label:"调解案件号",prop:"case_number","min-width":"150",align:"center"}),i(g,{label:"案件状态",prop:"case_status_cn","min-width":"120",align:"center"}),i(g,{label:"债权人",prop:"creditor_name","min-width":"120",align:"center"}),i(g,{label:"债务人",prop:"debtor_name","min-width":"120",align:"center"}),i(g,{label:"调解信息配置",align:"center","min-width":"220"},{default:v(({row:p})=>[l("div",Ua,[l("div",Fa,[(n(!0),_(X,null,W(p.mediation_config&&Array.isArray(p.mediation_config)?p.mediation_config.slice(0,4):[],T=>(n(),_("span",{key:T.id||T.title||Math.random(),class:"field-type-tag",title:T.title},L(T.title),9,Aa))),128)),p.mediation_config&&Array.isArray(p.mediation_config)&&p.mediation_config.length>4?(n(),_("span",Da," +"+L(p.mediation_config.length-4),1)):D("",!0)])])]),_:1}),i(g,{prop:"file_cn",label:"相关文件",align:"left","header-align":"center","min-width":"140"},{default:v(({row:p})=>[l("div",La,L(p.file_cn),1)]),_:1}),i(g,{label:"操作",width:"120",align:"center"},{default:v(({row:p})=>[l("div",xa,[l("div",{onClick:T=>se(p,"mediation"),class:"operation-btn edit-btn"},"编辑",8,Ra)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ie,O.value]])]),_:1})]),_:1},8,["modelValue"]),l("div",Na,[i(pe,{"current-page":C.value,"onUpdate:currentPage":f[2]||(f[2]=p=>C.value=p),"page-size":ve,total:y.value,layout:"total, prev, pager, next, jumper",onCurrentChange:Y},null,8,["current-page","total"])])]),M.value?(n(),A(fa,{key:0,visible:M.value,"row-data":o.value,"edit-mode":x.value,"onUpdate:visible":G,onConfirm:ne},null,8,["visible","row-data","edit-mode"])):D("",!0)],64)}}});const el=Ce(Ba,[["__scopeId","data-v-af614d93"]]);export{el as default};

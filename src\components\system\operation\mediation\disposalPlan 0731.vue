<script lang="ts" setup>
import { onMounted, ref, type Ref } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue';
import CustomInput from '@/components/common/CustomInput.vue';
import AddDisposalPlan from '../../dialogs/AddDisposalPlan.vue';
import EditDisposalPlan from '../../dialogs/EditDisposalPlan.vue';
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue';

import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { Plan, AddPlanParams, EditPlanParams, PlanField } from '../../auth/type';
import { getDataImportList,getMediationPlan, getStatusChoicesOptions, addMediationPlan, getMediationPlanDetail, editMediationPlan, deleteMediationPlan, getMediationCase,mediationPlanApprove } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 当前激活的标签页
const activeTab = ref<'asset' | 'mediationCases'>('asset')

const asset_package__isnull = ref(false)
// 选中的方案行数据
const selectRow: Ref<Plan> = ref({
  id: '',
  title: '',
  fields: [],
  jsonData: '',
  caseStatus: '',
  approvalStatus: '',
  schemeStatus: ''
})

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')

// 选项数据
const approval_status = ref('')
const plan_status = ref('')
const approvalStatusOptions = ref([])
const planStatusOptions = ref([])
const loadingOptions = ref(false)

// 响应式数据
const loading = ref(false)
const planList = ref<any[]>([])

// 对话框控制
const showAdd = ref(false)
const showEdit = ref(false)
const showDelete = ref(false)

// 资产包模式的下拉选项数据
const assetPackageOptions = ref([])
// 调解案件模式下拉框
const mediationCaseOptions = ref([])

// 获取审批状态和方案状态下拉框
async function  statusOptions() {
  loadingOptions.value = true
  const { data } = await getStatusChoicesOptions()
  const { state, msg } = data
  if(state === 'success') {
    const {approval_status_choices,plan_status_choices} = data.data;
    if (data.data) {
      approvalStatusOptions.value =approval_status_choices
      planStatusOptions.value = plan_status_choices
    }
  }else{
    ElMessage.error(msg)
  }
  loadingOptions.value = false
}

// 获取资产包下拉框数据
async function getAssetPackageOptions() {
  const {data} = await getDataImportList({page:1,page_size:1000})
  const { state, msg } = data
  if(state === 'success') {
    assetPackageOptions.value = data.data.results.map((item: any) => ({
      label: item.package_name,
      value: item.id
    }))
  } else {
    ElMessage.error(msg)
  }
}

// 获取调解案件下拉框数据
async function getMediationCaseOptions() {
  const { data } = await getMediationCase({page:1,page_size:1000})
	const { state, msg } = data
	if (state === 'success') {
		mediationCaseOptions.value = data.data.results.map((user: any) => ({
			label: user.case_number,
			value: user.id
		}))
	} else {
		ElMessage.error(msg || '获取数据失败')
	}
}


// 标签页切换处理
function handleTabChange(tabName: 'mediationCases' | 'asset') {
  activeTab.value = tabName
  page.value = 1
  // asset_package__isnull：true（未关联资产包）、false（已关联资产包） 如果是asset(资产包）则是false，反之
  if(tabName === 'asset') {
    asset_package__isnull.value = false
    getAssetPackageOptions()
  } else {
    asset_package__isnull.value = true
    getMediationCaseOptions()
  }
  searchPlanList()
}

// 重置到第一页并搜索
function handleSearch() {
  page.value = 1
  searchPlanList()
}

// 搜索方案列表（使用模拟数据）
async function searchPlanList() {
  loading.value = true
  /* plan_status: 方案状态（字符串），可选值：
  inactive: 未生效状态，方案尚未启用
  active: 已生效状态，方案正在使用
  approval_status: 审批状态（字符串），可选值：
  pending: 待审批状态，等待审批处理
  approved: 已通过状态，审批通过可以生效
  rejected: 未通过状态，审批被拒绝 */
  const params = {
    page: page.value,
    page_size,
    search: search.value,
    asset_package__isnull: asset_package__isnull.value,
    plan_status: plan_status.value,
    approval_status: approval_status.value,
  }
  const { data } = await getMediationPlan(params)
  const { state, msg } = data
  if(state === 'success') {
    const {results,count} = data.data
    planList.value = results
    total.value = count
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

// 分页改变
function pageChanged(p: number) {
  page.value = p
  searchPlanList()
}

// 打开新增对话框
function openAddPlanDialog() {
  showAdd.value = true 
}

// 提交新增调解案件
async function submitAddPlan(params: AddPlanParams) {
  try {
    // 构造接口参数，处理可选参数
    const requestData: any = {
      plan_name: params.plan_name,
      plan_config: params.plan_config
    }

    // 根据模式添加可选参数
    if (params.asset_package) {
      requestData.asset_package = params.asset_package
    }
    if (params.mediation_case) {
      requestData.mediation_case = params.mediation_case
    }

    console.log('新增调解案件参数:', requestData)

    // 调用真实接口
    const {data} = await addMediationPlan(requestData)
    const { state, msg } = data

    if(state === 'success') {
      ElMessage.success('新增成功')
      showAdd.value = false
      handleSearch() // 刷新列表
    } else {
      ElMessage.error(msg || '新增失败')
    }
  } catch (error) {
    console.error('新增调解案件失败:', error)
    ElMessage.error('新增失败')
  }
}

// 打开审批对话框
function openApprovalDialog(row: Plan, index: number) {
  selectRow.value = { ...row }
  // showApproval.value = true
}

// 打开编辑方案对话框
async function openEditPlanDialog(row: Plan, index: number) {
  const {data} = await getMediationPlanDetail(Number(row.id))
  const { state, msg } = data
  if(state === 'success') {
    selectRow.value = { ...data.data }
    // selectRow.value = { ...row }
  } else {
    ElMessage.error(msg)
  }
  showEdit.value = true
}

// 提交编辑调解案件
async function submitEditPlan(params: EditPlanParams) {
  const {data} = await editMediationPlan(params, Number(params.id))
  const { state, msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    showEdit.value = false
    searchPlanList() // 刷新列表
  } else {
    ElMessage.error(msg || '编辑失败')
  }
}

// 打开删除确认对话框
function openEnsureDeleteDialog(row: Plan, index: number) {
  selectRow.value = { ...row }
  showDelete.value = true
}

// 删除调解案件
async function deletePlanRow() {
  try {
    console.log('删除调解案件，ID:', selectRow.value.id)

    // 调用真实接口
    const {data} = await deleteMediationPlan(Number(selectRow.value.id))
    const { state, msg } = data

    if(state === 'success') {
      ElMessage.success('删除成功')
      showDelete.value = false
      searchPlanList() // 刷新列表
    } else {
      ElMessage.error(msg || '删除失败')
    }
  } catch (error) {
    console.error('删除调解案件失败:', error)
    ElMessage.error('删除失败')
  }
}

// 关闭编辑对话框
function closeEditDialog() {
  showEdit.value = false
  selectRow.value = {
    id: '',
    title: '',
    fields: [],
    jsonData: '',
    caseStatus: '',
    approvalStatus: '',
    schemeStatus: ''
  }
}

// 格式化显示动态字段概览
/* function formatFieldsPreview(plan_config: PlanField[]): string {
  if (!plan_config || plan_config.length === 0) return '无字段'
  return plan_config.map(plan_config => plan_config.title).slice(0, 3).join('、') +
         (plan_config.length > 3 ? '...' : '')
} */

// 组件挂载时获取方案列表和下拉框数据
onMounted(() => {
  searchPlanList()
  statusOptions()
  // 判断如果是资产包则调用getAssetPackageOptions，反之
  if(activeTab.value === 'asset'){
    getAssetPackageOptions()
  }else {
    getMediationCaseOptions()
  }
})
</script>

<template>
  <div class="plan-management">
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput v-model="search" placeholder="搜索资产包名称" @click="handleSearch"></CustomInput>
        </div>
        <div class="search-item">
          <label>审批状态</label>
          <el-select 
            v-model="approval_status" 
            placeholder="审批状态" 
            clearable
            @change="handleSearch"
            :loading="loadingOptions"
            style="width: 200px">
            <el-option
              v-for="option in approvalStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
        <div class="search-item">
          <label>方案状态</label>
          <el-select 
            v-model="plan_status" 
            placeholder="方案状态" 
            clearable
            @change="handleSearch"
            :loading="loadingOptions"
            style="width: 200px">
            <el-option
              v-for="option in planStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
        <div class="search-item">
          <CustomButton @click="openAddPlanDialog" :height="34"><i class="jt-20-add"></i>新增方案</CustomButton>
        </div>
      </div>
    </div>
    <!-- Tabs 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="plan-tabs">
      <el-tab-pane label="资产包" name="asset" class="asset-tab">
        <div>
          <el-table
            v-loading="loading"
            :data="planList"
            border
            :cell-style="cellStyle"
            :header-cell-style="headerCellStyle"
            class="plan-table">
            <el-table-column type="index" label="序号" width="60" align="center">
              <template v-slot="{$index}">
                {{page_size * (page - 1) + $index + 1}}
              </template>
            </el-table-column>
            <el-table-column prop="asset_package_name" label="资产包名称" align="center" min-width="200"/>
            <el-table-column prop="plan_name" label="方案名称" align="center" min-width="200"/>
            <el-table-column label="方案配置" align="center" min-width="220">
              <template v-slot="{row}">
                <div class="fields-preview">
                  <div class="field-types">
                    <!-- 修复TypeError: 添加防御性检查确保plan_config存在且为数组 -->
                    <span
                      v-for="field in (row.plan_config && Array.isArray(row.plan_config) ? row.plan_config.slice(0, 4) : [])"
                      :key="field.id || field.title || Math.random()"
                      class="field-type-tag"
                      :title="field.title">
                       {{ field.title }}
                    </span>
                    <span v-if="row.plan_config && Array.isArray(row.plan_config) && row.plan_config.length > 4" class="more-fields">
                      +{{ row.plan_config.length - 4 }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="plan_status_cn" label="方案状态" align="center" min-width="100">
              <template #default="{ row }">
                <span
                  :class="{
                    'text-success': row.plan_status_cn === '已生效',
                    'text-warning': row.plan_status_cn == '未生效'
                  }">
                  {{ row.plan_status_cn }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="250">
              <template v-slot="{row, $index}">
                <div class="operation-buttons">
                  <div @click="openEditPlanDialog(row, $index)" class="operation-btn edit-btn">编辑</div>
                  <div @click="openEnsureDeleteDialog(row, $index)" class="operation-btn delete-btn">删除</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="调解案件" name="mediationCases" class="mediation-cases-tab">
        <div>
          <el-table
            v-loading="loading"
            :data="planList"
            border
            :cell-style="cellStyle"
            :header-cell-style="headerCellStyle"
            class="plan-table">
            <el-table-column type="index" label="序号" width="60" align="center">
              <template v-slot="{$index}">
                {{page_size * (page - 1) + $index + 1}}
              </template>
            </el-table-column>
            <el-table-column prop="mediation_case_number" label="调解案件号" align="center" width="180"></el-table-column>
            <el-table-column prop="plan_name" label="方案名称" align="center" min-width="180"/>
            <el-table-column label="方案配置" align="center" min-width="220">
              <template v-slot="{row}">
                <div class="fields-preview">
                  <div class="field-types">
                    <span
                      v-for="field in (row.plan_config && Array.isArray(row.plan_config) ? row.plan_config.slice(0, 4) : [])"
                      :key="field.id || field.title || Math.random()"
                      class="field-type-tag"
                      :title="field.title">
                       {{ field.title }}
                    </span>
                    <span v-if="row.plan_config && Array.isArray(row.plan_config) && row.plan_config.length > 4" class="more-fields">
                      +{{ row.plan_config.length - 4 }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="approval_status_cn" label="审批状态" align="center" min-width="100">
              <template #default="{ row }">
                <span
                  :class="{
                    'text-success': row.approval_status_cn === '已通过',
                    'text-danger': row.approval_status_cn == '待审批',
                    'text-warning': row.approval_status_cn == '未通过'
                  }">
                  {{ row.approval_status_cn }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="plan_status_cn" label="方案状态" align="center" min-width="100">
              <template #default="{ row }">
                <span
                  :class="{
                    'text-success': row.plan_status_cn === '已生效',
                    'text-warning': row.plan_status_cn == '未生效'
                  }">
                  {{ row.plan_status_cn }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="250">
              <template v-slot="{row, $index}">
                <div class="operation-buttons">
                  <div @click="openApprovalDialog(row, $index)" class="operation-btn edit-btn">审批</div>
                  <div @click="openEditPlanDialog(row, $index)" class="operation-btn edit-btn">编辑</div>
                  <div @click="openEnsureDeleteDialog(row, $index)" class="operation-btn delete-btn">删除</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="page"
        :page-size="page_size"
        @current-change="pageChanged"
      ></el-pagination>
    </div>
  </div>

  <!-- 新增对话框 -->
  <AddDisposalPlan
    :show-dialog="showAdd"
    :current-mode="activeTab"
    :mediationCaseOptions="mediationCaseOptions"
    :assetPackageOptions="assetPackageOptions"
    @close="showAdd = false"
    @ensure="submitAddPlan">
  </AddDisposalPlan>

  <!-- 编辑方案对话框 -->
  <EditDisposalPlan
    :show-dialog="showEdit"
    :plan-data="selectRow"
    :current-mode="activeTab"
    :mediationCaseOptions="mediationCaseOptions"
    :assetPackageOptions="assetPackageOptions"
    @close="closeEditDialog"
    @ensure="submitEditPlan">
  </EditDisposalPlan>
  
  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDelete"
    title="删除调解方案"
    :message="`确认删除选中的调解方案吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDelete = $event"
    @confirm="deletePlanRow"
    @cancel="showDelete = false" />
</template>

<style lang="scss" scoped>
.plan-management {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }
  .plan-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
    
    :deep(.el-tabs__nav-wrap) {
      padding-left: 0;
    }
  }
  
}

.plan-table {
  border-radius: 8px;
  overflow: hidden;
  
  /* .plan-title {
    .title-text {
      font-weight: 600;
      color: #333;
      line-height: 1.4;
    }
  } */
  
  .fields-preview {
    .field-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
      
      .field-type-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        font-size: 12px;
        color: #0369a1;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .more-fields {
        padding: 2px 6px;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
  
  .field-stats {
    .required-count {
      margin-top: 4px;
    }
  }
  
  .create-time {
    font-size: 13px;
    color: #666;
  }
  .text-success{
    color:#1377C4;
  }
  .text-danger{
    color:#e6a23c;
  }
  .text-warning{
    color:#D94223;
  }
}

.pagination-wrapper {
  margin-top: 20px;
	display: flex;
	justify-content: center; 
}

// 响应式适配
@media (max-width: 1200px) {
  .plan-management {
    .search-header {
      grid-template-columns: 1fr auto;
    }
  }
  
  .plan-table {
    .fields-preview {
      .field-types {
        .field-type-tag {
          max-width: 60px;
        }
      }
    }
  }
}
</style>

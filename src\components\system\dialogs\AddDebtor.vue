<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import { ElMessage } from 'element-plus'
import type { 
  AddDebtorParams,
  DebtorTypeOption,
  CertificateTypeOption,
  PhoneInfo,
  EmailInfo,
  AddressInfo,
  WechatInfo
} from '../type'
import { getDebtorOptions } from '@/axios/system'

/**
 * 组件Props接口
 */
interface Props {
  visible: boolean
}

/**
 * 组件Emits接口
 */
interface Emits {
  (e: 'update:visible'): void
  (e: 'confirm', data: AddDebtorParams): void
}

// Props和Emits定义
const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// 表单引用和加载状态
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref<AddDebtorParams>({
  debtor_type: '',
  debtor_name: '',
  id_type: '',
  id_number: '',
  phones: [],
  emails: [],
  addresses: []
})

// 联系方式数据
const phoneList = ref<PhoneInfo[]>([{ phone: '', phone_type: '', is_primary: true }])
const emailList = ref<EmailInfo[]>([{ email: '', email_type: '', is_primary: true }])
const addressList = ref<AddressInfo[]>([{ address: '', address_type: '', is_primary: true }])
const wechatList = ref<WechatInfo[]>([{ wechat: '', wechat_type: '', is_primary: true }])

// 债权人类型选项配置
const DebtorTypeOptions = ref([])
const certificateTypeOptions = ref([])
const loadingOptions = ref(false)

/**
 * 获取选项数据
 */
async function fetchOptions() {
  loadingOptions.value = true
  const {data} = await getDebtorOptions()
  const { state , msg } = data
  if(state === 'success') {
    const {debtor_types,id_types} = data.data;
    if (data.data) {
      const debtorTypesArray = Object.entries(debtor_types).map(([key, value]) => ({
        label: value,
        value: key
      }));
      DebtorTypeOptions.value = debtorTypesArray;
      const idTypesArray = Object.entries(id_types).map(([key, value]) => ({
        label: value,
        value: key
      }));
      certificateTypeOptions.value = idTypesArray;
    }
  }else{
    ElMessage.error(msg)
  }
  loadingOptions.value = false
}

// 表单验证规则
const rules = {
  debtor_type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  debtor_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 100, message: '姓名长度在2到100个字符', trigger: 'blur' }
  ],
  id_type: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  id_number: [
    { required: true, message: '请输入证件号码', trigger: 'blur' },
    // { validator: validateCertificateNumber, trigger: 'blur' }
  ],
}

/**
 * 证件号码验证器
 */
/* function validateCertificateNumber(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入证件号码'))
    return
  }
  console.log(formData.value.id_type,'证件类型')
  switch (formData.value.id_type) {
    case "id_card":
      if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        callback(new Error('请输入正确的身份证号码'))
      } else {
        callback()
      }
      break
    case "passport":
      if (!/^[a-zA-Z0-9]{5,20}$/.test(value)) {
        callback(new Error('请输入正确的护照号码'))
      } else {
        callback()
      }
      break
    case "business_license":
      if (!/^[0-9A-Z]{18}$/.test(value)) {
        callback(new Error('请输入正确的营业执照号码（18位）'))
      } else {
        callback()
      }
      break
    default:
      callback()
  }
} */

/**
 * 监听弹框显示状态，初始化表单数据
 */
watch(() => props.visible, async (visible) => {
  if (visible) {
    await fetchOptions()
    initFormData()
  }
})

/**
 * 监听债权人类型变化，自动调整证件类型
 */
/* watch(() => formData.value.type, (newType) => {
  if (newType === DebtorTypeOptions.value.individual) {
    // 个人默认选择身份证
    formData.value.id_type = certificateTypeOptions.value.id_card
  } else {
    // 企业默认选择营业执照
    formData.value.id_type = certificateTypeOptions.value.business_license
  }
  // 清空证件号码
  formData.value.id_number = ''
}) */

/**
 * 初始化表单数据
 */
function initFormData() {
  formData.value = {
    debtor_name: '',
    debtor_type: '',
    id_type: '',
    id_number: '',
    phones: [],
    emails: [],
    addresses: []
  }
  
  // 重置联系方式列表
  phoneList.value = [{ phone: '', phone_type: '', is_primary: true }]
  emailList.value = [{ email: '', email_type: '', is_primary: true }]
  addressList.value = [{ address: '', address_type: '', is_primary: true }]
  wechatList.value = [{ wechat: '', wechat_type: '', is_primary: true }]
  
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

/**
 * 关闭弹框
 */
function closeDialog() {
  emit('update:visible')
}

/**
 * 添加电话
 */
function addPhone() {
  phoneList.value.push({ phone: '', phone_type: '', is_primary: false })
}

/**
 * 删除电话
 */
function removePhone(index: number) {
  if (phoneList.value.length > 1) {
    phoneList.value.splice(index, 1)
    // 如果删除的是主要电话，设置第一个为主要电话
    if (!phoneList.value.find(p => p.is_primary) && phoneList.value.length > 0) {
      phoneList.value[0].is_primary = true
    }
  }
}

/**
 * 设置主要电话
 */
function setPrimaryPhone(index: number) {
  phoneList.value.forEach((phone, i) => {
    phone.is_primary = i === index
  })
}

/**
 * 添加邮箱
 */
function addEmail() {
  emailList.value.push({ email: '', email_type: '', is_primary: false })
}

/**
 * 删除邮箱
 */
function removeEmail(index: number) {
  if (emailList.value.length > 1) {
    emailList.value.splice(index, 1)
    // 如果删除的是主要邮箱，设置第一个为主要邮箱
    if (!emailList.value.find(e => e.is_primary) && emailList.value.length > 0) {
      emailList.value[0].is_primary = true
    }
  }
}

/**
 * 设置主要邮箱
 */
function setPrimaryEmail(index: number) {
  emailList.value.forEach((email, i) => {
    email.is_primary = i === index
  })
}

/**
 * 添加地址
 */
function addAddress() {
  addressList.value.push({ address: '', address_type: '', is_primary: false })
}

/**
 * 删除地址
 */
function removeAddress(index: number) {
  if (addressList.value.length > 1) {
    addressList.value.splice(index, 1)
    // 如果删除的是主要地址，设置第一个为主要地址
    if (!addressList.value.find(a => a.is_primary) && addressList.value.length > 0) {
      addressList.value[0].is_primary = true
    }
  }
}

/**
 * 设置主要地址
 */
function setPrimaryAddress(index: number) {
  addressList.value.forEach((address, i) => {
    address.is_primary = i === index
  })
}

/**
 * 添加微信
 */
function addWechat() {
  wechatList.value.push({ wechat: '', wechat_type: '', is_primary: false })
}

/**
 * 删除微信
 */
function removeWechat(index: number) {
  if (wechatList.value.length > 1) {
    wechatList.value.splice(index, 1)
    // 如果删除的是主要微信，设置第一个为主要微信
    if (!wechatList.value.find(w => w.is_primary) && wechatList.value.length > 0) {
      wechatList.value[0].is_primary = true
    }
  }
}

/**
 * 设置主要微信
 */
function setPrimaryWechat(index: number) {
  wechatList.value.forEach((wechat, i) => {
    wechat.is_primary = i === index
  })
}

/**
 * 确认提交
 */
async function confirmSubmit() {
  if (!formRef.value) return
  
  loading.value = true
  try {
    // 验证表单
    const isValid = await new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValid) {
      return
    }
    
    // 过滤空的联系方式
    const validPhones = phoneList.value.filter(p => p.phone.trim())
    const validEmails = emailList.value.filter(e => e.email.trim())
    const validAddresses = addressList.value.filter(a => a.address.trim())
    const validWechats = wechatList.value.filter(w => w.wechat.trim())
    
    // 提交数据
    const submitData: AddDebtorParams = {
      ...formData.value,
      phones: validPhones.length > 0 ? validPhones : undefined,
      emails: validEmails.length > 0 ? validEmails : undefined,
      addresses: validAddresses.length > 0 ? validAddresses : undefined,
      wechats: validWechats.length > 0 ? validWechats : undefined
    }
    
    emit('confirm', submitData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error(error)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog
    :visible="props.visible"
    title="新增债务人"
    width="600px"
    @update:visible="closeDialog">
    
    <div v-loading="loading" class="add-creditor-form">
      <el-form 
        ref="formRef"
        :model="formData" 
        :rules="rules"
        label-width="120px" 
        label-position="right">
        <el-form-item label="类型" prop="debtor_type">
          <el-select 
            v-model="formData.debtor_type" 
            placeholder="请选择债权人类型"
            :loading="loadingOptions"
            style="width: 100%">
            <el-option
              v-for="option in DebtorTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="debtor_name">
          <el-input 
            v-model="formData.debtor_name" 
            placeholder="请输入债权人姓名"/>
        </el-form-item>
        <el-form-item label="证件类型" prop="id_type">
          <el-select 
            v-model="formData.id_type" 
            placeholder="请选择证件类型"
            :loading="loadingOptions"
            style="width: 100%">
            <el-option
              v-for="option in certificateTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码" prop="id_number">
          <el-input 
            v-model="formData.id_number" 
            placeholder="请输入证件号码"
            maxlength="20" />
        </el-form-item>
        <!-- 联系电话 -->
        <el-form-item label="联系电话">
          <div class="contact-list">
            <div v-for="(phone, index) in phoneList" :key="index" class="contact-item">
              <div class="contact-input-group">
                <el-input 
                  v-model="phone.phone" 
                  placeholder="请输入联系电话"
                  maxlength="11" 
                  style="flex: 1" />
                <el-radio 
                  v-model="phone.is_primary" 
                  :label="true"
                  @change="setPrimaryPhone(index)"
                  class="primary-radio">主要</el-radio>
                <el-button 
                  v-if="phoneList.length > 1"
                  @click="removePhone(index)" 
                  type="danger" 
                  text 
                  size="small">删除</el-button>
              </div>
            </div>
            <el-button @click="addPhone" type="primary" text size="small">
              <i class="el-icon-plus"></i> 添加电话
            </el-button>
          </div>
        </el-form-item>
        
        <!-- 联系邮箱 -->
        <el-form-item label="联系邮箱">
          <div class="contact-list">
            <div v-for="(email, index) in emailList" :key="index" class="contact-item">
              <div class="contact-input-group">
                <el-input 
                  v-model="email.email" 
                  placeholder="请输入联系邮箱"
                  maxlength="50" 
                  style="flex: 1" />
                <el-radio 
                  v-model="email.is_primary" 
                  :label="true"
                  @change="setPrimaryEmail(index)"
                  class="primary-radio">主要</el-radio>
                <el-button 
                  v-if="emailList.length > 1"
                  @click="removeEmail(index)" 
                  type="danger" 
                  text 
                  size="small">删除</el-button>
              </div>
            </div>
            <el-button @click="addEmail" type="primary" text size="small">
              <i class="el-icon-plus"></i> 添加邮箱
            </el-button>
          </div>
        </el-form-item>
        
        <!-- 联系地址 -->
        <el-form-item label="联系地址">
          <div class="contact-list">
            <div v-for="(address, index) in addressList" :key="index" class="contact-item">
              <div class="contact-input-group">
                <el-input 
                  v-model="address.address" 
                  type="textarea"
                  :rows="2"
                  placeholder="请输入联系地址"
                  maxlength="200"
                  style="flex: 1" />
                <div class="contact-controls">
                  <el-radio 
                    v-model="address.is_primary" 
                    :label="true"
                    @change="setPrimaryAddress(index)"
                    class="primary-radio">主要</el-radio>
                  <el-button 
                    v-if="addressList.length > 1"
                    @click="removeAddress(index)" 
                    type="danger" 
                    text 
                    size="small">删除</el-button>
                </div>
              </div>
            </div>
            <el-button @click="addAddress" type="primary" text size="small">
              <i class="el-icon-plus"></i> 添加地址
            </el-button>
          </div>
        </el-form-item>

        <!-- 联系微信 -->
        <el-form-item label="联系微信">
          <div class="contact-list">
            <div v-for="(wechat, index) in wechatList" :key="index" class="contact-item">
              <div class="contact-input-group">
                <el-input 
                  v-model="wechat.wechat"
                  placeholder="请输入联系微信"
                  maxlength="50" 
                  style="flex: 1" />
                <el-radio 
                  v-model="wechat.is_primary" 
                  :label="true"
                  @change="setPrimaryWechat(index)"
                  class="primary-radio">主要</el-radio>
                <el-button 
                  v-if="wechatList.length > 1"
                  @click="removeWechat(index)" 
                  type="danger" 
                  text 
                  size="small">删除</el-button>
              </div>
            </div>
            <el-button @click="addWechat" type="primary" text size="small">
              <i class="el-icon-plus"></i> 添加微信
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <div class="dialog-footer">
        <CustomButton @click="confirmSubmit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="closeDialog" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.add-creditor-form {
  padding: 0 20px;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 16px 0 8px 0;
}

.contact-list {
  width: 100%;
  
  .contact-item {
    margin-bottom: 10px;
    .contact-input-group {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .primary-radio {
        white-space: nowrap;
      }
      
      .contact-controls {
        display: flex;
        align-items: center;
      }
    }
  }
}
</style> 
/* empty css             */import{C as he,h as fe,c as me}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as F,o as n,q as P,w as $,e as c,f as e,t as o,F as C,B as D,G as E,p as L,m as N,r as v,c as q,P as ge,A as be,g as d,J as we,K as $e,n as J,E as m,Q as ye,S as ke,I as Ce,T as Se,U as Pe,L as De,M as Ie,N as Ve,h as Q,V as Me,W as Ee,X as Ue,O as ze}from"./index-d4ffb1a1.js";import{C as Ae}from"./CustomButton-777ba9d4.js";import{_ as W}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";import{_ as R}from"./_plugin-vue_export-helper-c27b6911.js";const g=_=>(L("data-v-b5b6de4f"),_=_(),N(),_),Be={key:0,class:"preview-content"},Te={class:"info-section"},Oe=g(()=>e("h3",{class:"section-title"},"基本信息",-1)),Fe={class:"info-grid"},Le={class:"info-item"},Ne=g(()=>e("span",{class:"label"},"调解案件号：",-1)),Re={class:"value"},Ke={class:"info-item"},Ge=g(()=>e("span",{class:"label"},"案件状态：",-1)),Xe={class:"value"},qe={class:"info-item"},Je=g(()=>e("span",{class:"label"},"债权人：",-1)),Qe={class:"value"},We={class:"info-item"},Ze=g(()=>e("span",{class:"label"},"债务人：",-1)),je={class:"value"},He={class:"info-item"},Ye=g(()=>e("span",{class:"label"},"资产包名称：",-1)),xe={class:"value"},ea={class:"info-item"},aa=g(()=>e("span",{class:"label"},"调解员：",-1)),sa={class:"value"},ta={key:0,class:"info-section"},la=g(()=>e("h3",{class:"section-title"},"调解信息",-1)),na={class:"config-list"},oa={class:"config-title"},ia={class:"config-value"},ca={key:1,class:"info-section"},da=g(()=>e("h3",{class:"section-title"},"相关文件",-1)),ra={class:"file-list"},_a={class:"file-info"},ua={class:"file-name"},va={key:2,class:"info-section"},pa=g(()=>e("h3",{class:"section-title"},"相关文件",-1)),ha={class:"file-content"},fa={key:1,class:"empty-state"},ma=g(()=>e("p",null,"暂无数据",-1)),ga=[ma],ba=F({__name:"MediationInfoPreviewDialog",props:{visible:{type:Boolean,default:!1},data:{default:null}},emits:["update:visible","close"],setup(_,{emit:i}){function b(){i("update:visible",!1),i("close")}return(t,I)=>(n(),P(W,{title:"预览调解信息",visible:t.visible,width:"800px",markclose:!0,"onUpdate:visible":b},{default:$(()=>[t.data?(n(),c("div",Be,[e("div",Te,[Oe,e("div",Fe,[e("div",Le,[Ne,e("span",Re,o(t.data.case_number||"-"),1)]),e("div",Ke,[Ge,e("span",Xe,o(t.data.case_status_cn||"-"),1)]),e("div",qe,[Je,e("span",Qe,o(t.data.creditor_name||"-"),1)]),e("div",We,[Ze,e("span",je,o(t.data.debtor_name||"-"),1)]),e("div",He,[Ye,e("span",xe,o(t.data.asset_package_name||"-"),1)]),e("div",ea,[aa,e("span",sa,o(t.data.mediator_name||"-"),1)])])]),t.data.mediation_config&&t.data.mediation_config.length>0?(n(),c("div",ta,[la,e("div",na,[(n(!0),c(C,null,D(t.data.mediation_config,(p,h)=>(n(),c("div",{key:h,class:"config-item"},[e("div",oa,o(p.title||p.field_name||`字段${h+1}`),1),e("div",ia,o(p.value||p.field_value||"-"),1)]))),128))])])):E("",!0),t.data.attachments&&t.data.attachments.length>0?(n(),c("div",ca,[da,e("div",ra,[(n(!0),c(C,null,D(t.data.attachments,(p,h)=>(n(),c("div",{key:h,class:"file-item"},[e("div",_a,[e("span",ua,o(p.name),1)])]))),128))])])):t.data.file_cn?(n(),c("div",va,[pa,e("div",ha,[e("pre",null,o(t.data.file_cn),1)])])):E("",!0)])):(n(),c("div",fa,ga))]),_:1},8,["visible"]))}});const wa=R(ba,[["__scopeId","data-v-b5b6de4f"]]),y=_=>(L("data-v-5fd90c83"),_=_(),N(),_),$a={class:"preview-content"},ya={key:0,class:"info-section"},ka=y(()=>e("h3",{class:"section-title"},"基本信息",-1)),Ca={class:"info-grid"},Sa={class:"info-item"},Pa=y(()=>e("span",{class:"label"},"调解案件号：",-1)),Da={class:"value"},Ia={class:"info-item"},Va=y(()=>e("span",{class:"label"},"案件状态：",-1)),Ma={class:"value"},Ea={class:"info-item"},Ua=y(()=>e("span",{class:"label"},"债权人：",-1)),za={class:"value"},Aa={class:"info-item"},Ba=y(()=>e("span",{class:"label"},"债务人：",-1)),Ta={class:"value"},Oa={class:"info-item"},Fa=y(()=>e("span",{class:"label"},"资产包名称：",-1)),La={class:"value"},Na={class:"info-item"},Ra=y(()=>e("span",{class:"label"},"调解员：",-1)),Ka={class:"value"},Ga={class:"info-section"},Xa=y(()=>e("h3",{class:"section-title"},"调解方案详情",-1)),qa={class:"scheme-title"},Ja={class:"plan-label"},Qa={class:"plan-value"},Wa={key:1,class:"empty-state"},Za=y(()=>e("p",null,"暂无调解方案数据",-1)),ja=[Za],Ha=F({__name:"MediationPlanPreviewDialog",props:{visible:{type:Boolean,default:!1},rowData:{default:null},data:{default:null}},emits:["update:visible","close"],setup(_,{emit:i}){function b(){i("update:visible",!1),i("close")}return(t,I)=>(n(),P(W,{title:"预览调解方案内容",visible:t.visible,width:"900px",markclose:!0,"onUpdate:visible":b},{default:$(()=>[e("div",$a,[t.rowData?(n(),c("div",ya,[ka,e("div",Ca,[e("div",Sa,[Pa,e("span",Da,o(t.rowData.case_number||"-"),1)]),e("div",Ia,[Va,e("span",Ma,o(t.rowData.case_status_cn||"-"),1)]),e("div",Ea,[Ua,e("span",za,o(t.rowData.creditor_name||"-"),1)]),e("div",Aa,[Ba,e("span",Ta,o(t.rowData.debtor_name||"-"),1)]),e("div",Oa,[Fa,e("span",La,o(t.rowData.asset_package_name||"-"),1)]),e("div",Na,[Ra,e("span",Ka,o(t.rowData.mediator_name||"-"),1)])])])):E("",!0),e("div",Ga,[Xa,t.data.length>0?(n(!0),c(C,{key:0},D(t.data,(p,h)=>(n(),c("div",{key:h,class:"scheme-container"},[e("div",qa,o(p.plan_name),1),p.plan_config.length>0?(n(!0),c(C,{key:0},D(p.plan_config,(V,w)=>(n(),c("div",{key:"config"+w,class:"plan-row"},[e("div",Ja,o(V.title)+"：",1),e("div",Qa,o(V.value),1)]))),128)):E("",!0)]))),128)):(n(),c("div",Wa,ja))])])]),_:1},8,["visible"]))}});const Ya=R(Ha,[["__scopeId","data-v-5fd90c83"]]),Z=_=>(L("data-v-fbf4634c"),_=_(),N(),_),xa={class:"case-tracking"},es={class:"search-header"},as={class:"search-row"},ss={class:"search-item"},ts={class:"search-item"},ls=Z(()=>e("label",null,"资产包",-1)),ns={class:"search-item"},os=Z(()=>e("label",null,"案件状态",-1)),is={class:"search-row"},cs={class:"table-container"},ds={style:{"white-space":"pre-wrap !important"}},rs={class:"operation-buttons"},_s=["onClick"],us=["onClick"],vs={class:"pagination-container"},ps=9999,hs=F({__name:"caseTracking",setup(_){const i=v({search:"",page:1,page_size:10}),b=q(()=>w.value&&k.value==="draft"),t=v(!1),I=v([]),p=v(0),h=v([]),V=v(!1),w=v(""),k=v(""),K=v([]),G=v([]),U=v(!1),z=v(!1),T=v(null),S=v(null),j=ge(()=>{i.value.page=1,B()},300);function A(){j()}function O(s){h.value=s,X()}function H(s){O(s)}function Y(s){O(s)}function x(s,a){O(s)}function X(){if(b.value){const s=I.value.filter(a=>a.case_status==="draft");V.value=s.length>0&&h.value.length===s.length}}function ee(s){return s.case_status==="draft"}const ae=q(()=>!w.value||k.value!=="draft"?!1:b.value?h.value.length>0:!0);function se(){return w.value?k.value!=="draft"?{isValid:!1,message:'请将案件状态设置为"待发起"'}:b.value&&h.value.length===0?{isValid:!1,message:"请先选择要发起的案件"}:{isValid:!0}:{isValid:!1,message:"请先选择资产包"}}async function te(){const s=se();if(!s.isValid){m.warning(s.message);return}if(b.value){const a=h.value.map(M=>M.id),{data:u}=await ye({case_ids:a}),{state:f,msg:r}=u;f==="success"?(m.success(r),le(),B()):m.error(r||"发起操作失败")}else m.success("发起操作已执行")}function le(){h.value=[],V.value=!1}async function B(){t.value=!0;try{const s={page:i.value.page,page_size:b.value?ps:i.value.page_size,search:i.value.search};w.value&&(s.asset_package=w.value),k.value&&(s.case_status=k.value);const{data:a}=await ke(s),{state:u,msg:f}=a;if(u==="success"){const{results:r,count:M}=a.data;I.value=r,p.value=M,X()}else m.error(f)}finally{t.value=!1}}async function ne(){const{data:s}=await Ce({page:1,page_size:1e3,package_status:"available"}),{state:a,msg:u}=s;a==="success"?K.value=s.data.results.map(f=>({label:f.package_name,value:f.id})):m.error(u)}async function oe(){const{data:s}=await Se(),{state:a,msg:u}=s;a==="success"?G.value=s.data.case_status_choices:m.error(u)}async function ie(s){try{const{data:a}=await Me(s.case_number),{state:u,msg:f}=a;u==="success"?(S.value={...s,...a.data},U.value=!0):m.error(f||"获取调解信息失败")}catch(a){console.error("获取调解信息失败:",a),m.error("获取调解信息失败，请重试")}}async function ce(s){try{const{data:a}=await Ee(s.case_number),{state:u,msg:f}=a;u==="success"?(T.value=s,S.value=a.data,z.value=!0):m.error(f||"获取调解方案失败")}catch(a){console.error("获取调解方案失败:",a),m.error("获取调解方案失败，请重试")}}function de(){U.value=!1,S.value=null}function re(){z.value=!1,T.value=null,S.value=null}function _e(s){i.value.page=s,B()}return be(()=>{B(),ne(),oe()}),(s,a)=>{const u=Ue,f=Pe,r=ze,M=De,ue=Ie,ve=Ve;return n(),c(C,null,[e("div",xa,[e("div",es,[e("div",as,[e("div",ss,[d(he,{modelValue:i.value.search,"onUpdate:modelValue":a[0]||(a[0]=l=>i.value.search=l),placeholder:"请输入调解案件号",class:"search-input",onKeydown:we(A,["enter"]),onClick:A},null,8,["modelValue","onKeydown"])]),e("div",ts,[ls,d(f,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=l=>w.value=l),placeholder:"资产包",clearable:"",onChange:A},{default:$(()=>[(n(!0),c(C,null,D(K.value,l=>(n(),P(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",ns,[os,d(f,{modelValue:k.value,"onUpdate:modelValue":a[2]||(a[2]=l=>k.value=l),placeholder:"案件状态",clearable:"",onChange:A},{default:$(()=>[(n(!0),c(C,null,D(G.value,l=>(n(),P(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",is,[d(Ae,{onClick:te,height:34,disabled:!ae.value,"btn-type":"blue"},{default:$(()=>[Q(" 发起 ")]),_:1},8,["disabled"])])]),e("div",cs,[$e((n(),P(M,{data:I.value,style:{width:"100%"},"header-cell-style":J(fe),"cell-style":J(me),border:"",stripe:"",onSelectionChange:H,onSelectAll:Y,onSelect:x},{default:$(()=>[b.value?(n(),P(r,{key:0,type:"selection",width:"55",selectable:ee})):E("",!0),d(r,{label:"序号",type:"index",width:"80",align:"center"},{default:$(({$index:l})=>[Q(o(i.value.page_size*(i.value.page-1)+l+1),1)]),_:1}),d(r,{prop:"case_number",label:"调解案件号",align:"center",width:"210"}),d(r,{prop:"case_status_cn",label:"案件状态",align:"center",width:"100"}),d(r,{prop:"creditor_name",label:"债权人",align:"center",width:"200"}),d(r,{prop:"debtor_name",label:"债务人",align:"center",width:"200"}),d(r,{prop:"asset_package_name",label:"资产包名称",align:"center",width:"200"}),d(r,{prop:"mediator_name",label:"调解员",align:"center",width:"100"}),d(r,{prop:"file_cn",label:"相关文件",align:"left","header-align":"center","min-width":"150"},{default:$(({row:l})=>[e("div",ds,o(l.file_cn),1)]),_:1}),d(r,{label:"操作",width:"180",align:"center","header-align":"center"},{default:$(({row:l})=>[e("div",rs,[e("div",{onClick:pe=>ie(l),class:"operation-btn preview-btn"},"预览调解信息内容",8,_s),e("div",{onClick:pe=>ce(l),class:"operation-btn preview-btn"},"预览调解方案内容",8,us)])]),_:1})]),_:1},8,["data","header-cell-style","cell-style"])),[[ve,t.value]])]),e("div",vs,[d(ue,{class:"page",background:"","current-page":i.value.page,"onUpdate:currentPage":a[3]||(a[3]=l=>i.value.page=l),"page-size":i.value.page_size,"onUpdate:pageSize":a[4]||(a[4]=l=>i.value.page_size=l),total:p.value,layout:"prev, pager, next",onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),d(wa,{visible:U.value,data:S.value,"onUpdate:visible":a[5]||(a[5]=l=>U.value=l),onClose:de},null,8,["visible","data"]),d(Ya,{visible:z.value,rowData:T.value,data:S.value,"onUpdate:visible":a[6]||(a[6]=l=>z.value=l),onClose:re},null,8,["visible","rowData","data"])],64)}}});const Cs=R(hs,[["__scopeId","data-v-fbf4634c"]]);export{Cs as default};

/* empty css             */import{C as ae,c as te,h as ne}from"./headerCellStyle-3128036a.js";/* empty css                      *//* empty css                 */import{d as j,r as m,c as oe,v as O,o as f,q as z,w as o,K,e as k,g as e,f as u,F as M,B as N,h as S,E as g,j as se,k as ie,av as ue,X as A,U as q,l as ce,N as G,p as J,m as X,A as de,J as re,n as L,G as pe,aA as _e,aB as me,L as ve,M as be,t as I,O as he,aC as fe}from"./index-d4ffb1a1.js";import{_ as ge}from"./CustomDialog.vue_vue_type_style_index_0_lang-5d3a03de.js";/* empty css                     *//* empty css                       *//* empty css                  */import{C as P}from"./CustomButton-777ba9d4.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const Q=v=>(J("data-v-a72493ec"),v=v(),X(),v),ye={class:"edit-complaint-form"},we={class:"readonly-section"},De={class:"editable-section"},ke={class:"dialog-footer"},Ce=Q(()=>u("i",{class:"jt-20-ensure"},null,-1)),Ve=Q(()=>u("i",{class:"jt-20-delete"},null,-1)),Ee=j({__name:"EditComplaintDialog",props:{visible:{type:Boolean,default:!1},complaintData:{default:null}},emits:["update:visible","confirm"],setup(v,{emit:w}){const i=v,C=localStorage.getItem("userName"),p=m(!1),a=m({handler:C||"",handle_time:"",process_status:"",handle_result:""}),V=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已解决",value:"resolved"},{label:"已关闭",value:"closed"}],E=[{label:"意见建议",value:"suggestion"},{label:"服务投诉",value:"complaint "}],x=oe(()=>{if(!i.complaintData)return"";const n=E.find(s=>s.value===i.complaintData.feedback_type_cn);return(n==null?void 0:n.label)||i.complaintData.feedback_type_cn});O(()=>[i.visible,i.complaintData],([n,s])=>{n&&s&&typeof s=="object"&&y(s.id)},{immediate:!0});async function y(n){if(i.complaintData)try{p.value=!0,console.log("获取投诉反馈详细信息:",n),await new Promise(s=>setTimeout(s,500)),a.value={handler:i.complaintData.handler||"",handle_time:i.complaintData.handle_time||"",process_status:i.complaintData.process_status,handle_result:i.complaintData.handle_result||""},console.log("获取详细信息成功:",a.value)}catch(s){console.error("获取详细信息失败:",s),g.error("获取详细信息失败")}finally{p.value=!1}}function F(){w("update:visible")}async function T(){if(!i.complaintData){g.warning("数据异常，请重试");return}if(!a.value.process_status){g.warning("请选择处理状态");return}if(a.value.process_status==="已解决"&&!a.value.handle_result.trim()){g.warning("处理状态为已解决时，处理结果不能为空");return}try{p.value=!0,console.log("提交处理结果:",{id:i.complaintData.id,...a.value}),await new Promise(n=>setTimeout(n,1e3)),w("confirm",{handler:a.value.handler,handle_time:a.value.handle_time,process_status:a.value.process_status,handle_result:a.value.handle_result.trim()}),console.log("处理完成")}catch(n){console.error("处理失败:",n),g.error("处理失败，请重试")}finally{p.value=!1}}function D(){a.value={handler:"",handle_time:"",process_status:"",handle_result:""}}return O(()=>i.visible,n=>{n||D()}),(n,s)=>{const b=se,_=ie,U=ue,$=A,d=q,c=ce,h=G;return f(),z(ge,{visible:i.visible,title:"编辑投诉反馈",width:"600px","onUpdate:visible":F},{default:o(()=>[K((f(),k("div",ye,[e(c,{model:a.value,"label-width":"120px","label-position":"right"},{default:o(()=>[u("div",we,[e(_,{label:"案件编号："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.case_number,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1}),e(_,{label:"反馈类型："},{default:o(()=>[e(b,{value:x.value,disabled:"",class:"readonly-input"},null,8,["value"])]),_:1}),e(_,{label:"具体类别："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.category_cn,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1}),e(_,{label:"详细描述："},{default:o(()=>{var l;return[e(b,{type:"textarea",value:(l=n.complaintData)==null?void 0:l.description,disabled:"",rows:3,class:"readonly-textarea"},null,8,["value"])]}),_:1}),e(_,{label:"联系方式："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.phone_number,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1})]),u("div",De,[e(_,{label:"处理人："},{default:o(()=>[e(b,{modelValue:a.value.handler,"onUpdate:modelValue":s[0]||(s[0]=l=>a.value.handler=l),placeholder:"请输入处理人"},null,8,["modelValue"])]),_:1}),e(_,{label:"处理时间："},{default:o(()=>[e(U,{modelValue:a.value.handle_time,"onUpdate:modelValue":s[1]||(s[1]=l=>a.value.handle_time=l),type:"date",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",placeholder:"请选择处理时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(_,{label:"处理状态："},{default:o(()=>[e(d,{modelValue:a.value.process_status,"onUpdate:modelValue":s[2]||(s[2]=l=>a.value.process_status=l),placeholder:"请选择处理状态",style:{width:"100%"}},{default:o(()=>[(f(),k(M,null,N(V,l=>e($,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"处理结果："},{default:o(()=>[e(b,{modelValue:a.value.handle_result,"onUpdate:modelValue":s[3]||(s[3]=l=>a.value.handle_result=l),type:"textarea",placeholder:"请输入处理结果",rows:4,maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"]),u("div",ke,[e(P,{onClick:T,height:34,loading:p.value,"btn-type":"blue"},{default:o(()=>[Ce,S("确认 ")]),_:1},8,["loading"]),e(P,{onClick:F,height:34},{default:o(()=>[Ve,S("取消 ")]),_:1})])])),[[h,p.value]])]),_:1},8,["visible"])}}});const xe=H(Ee,[["__scopeId","data-v-a72493ec"]]),R=v=>(J("data-v-6816b05d"),v=v(),X(),v),Se={class:"complaint-feedback-page"},Fe={class:"search-header"},Ie={class:"search-row"},Te={class:"search-item"},Ue={class:"search-item"},$e=R(()=>u("label",null,"处理状态：",-1)),Be={class:"search-item"},Ye=R(()=>u("label",null,"反馈类型：",-1)),Me={class:"table-container"},Ne={class:"result-text"},Oe={class:"operation-buttons"},Le=["onClick"],Pe={key:0,class:"pagination-wrapper"},Y=10,je=j({__name:"complaintFeedback",setup(v){const w=m(!1),i=m([]),C=m(0),p=m(1),a=m(""),V=m(""),E=m(""),x=m(!1),y=m(null),F=[{label:"意见建议",value:"suggestion"},{label:"服务投诉",value:"complaint "}],T=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已解决",value:"resolved"},{label:"已关闭",value:"closed"}];async function D(){w.value=!0;const d={page:p.value,page_size:Y,search:a.value,process_status:V.value,feedback_type:E.value},{data:c}=await _e(d),{state:h,msg:l}=c;if(h=="success"){const{results:r,count:B}=c.data;i.value=r,C.value=B}else g.error(l);w.value=!1}function n(){p.value=1,D()}function s(d){p.value=d,D()}async function b(d,c){y.value=d,x.value=!0}function _(){x.value=!1,y.value=null}async function U(d){if(!y.value)return;const{data:c}=await me(d,y.value.id),{state:h,msg:l}=c;h=="success"?(g.success(l),D(),_()):g.error(l)}function $(d){return d?d.split("T")[0]:" "}return de(()=>{D()}),(d,c)=>{const h=A,l=q,r=he,B=fe,W=ve,Z=be,ee=G;return f(),k("div",Se,[u("div",Fe,[u("div",Ie,[u("div",Te,[e(ae,{modelValue:a.value,"onUpdate:modelValue":c[0]||(c[0]=t=>a.value=t),placeholder:"搜索案件编号",onKeyup:re(n,["enter"]),onClick:n},null,8,["modelValue","onKeyup"])]),u("div",Ue,[$e,e(l,{modelValue:V.value,"onUpdate:modelValue":c[1]||(c[1]=t=>V.value=t),placeholder:"请选择处理状态",clearable:"",onChange:n},{default:o(()=>[(f(),k(M,null,N(T,t=>e(h,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),u("div",Be,[Ye,e(l,{modelValue:E.value,"onUpdate:modelValue":c[2]||(c[2]=t=>E.value=t),placeholder:"请选择反馈类型",clearable:"",onChange:n},{default:o(()=>[(f(),k(M,null,N(F,t=>e(h,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])])]),u("div",Me,[K((f(),z(W,{data:i.value,border:"",style:{width:"100%"},"cell-style":L(te),"header-cell-style":L(ne)},{default:o(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"},{default:o(({$index:t})=>[S(I((p.value-1)*Y+t+1),1)]),_:1}),e(r,{prop:"case_number",label:"案件编号",width:"150",align:"center"}),e(r,{label:"反馈类型",width:"100",align:"center"},{default:o(({row:t})=>[e(B,{type:t.feedback_type_cn==="服务投诉"?"danger":"primary",size:"small"},{default:o(()=>[S(I(t.feedback_type_cn),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"category_cn",label:"具体类别",width:"190",align:"center"}),e(r,{prop:"description",label:"详细描述","min-width":"200",align:"left"}),e(r,{prop:"phone_number",label:"联系方式",width:"130",align:"center"}),e(r,{prop:"process_status_cn",label:"处理状态",width:"100",align:"center"}),e(r,{prop:"handler",label:"处理人",width:"100",align:"center"}),e(r,{label:"处理时间",width:"120",align:"center"},{default:o(({row:t})=>[S(I($(t.handle_time)),1)]),_:1}),e(r,{label:"处理结果","min-width":"150",align:"left"},{default:o(({row:t})=>[u("div",Ne,I(t.handle_result),1)]),_:1}),e(r,{label:"操作",width:"100",align:"center",fixed:"right"},{default:o(({row:t,$index:le})=>[u("div",Oe,[u("div",{class:"operation-btn edit-btn",onClick:ze=>b(t,le)},"编辑",8,Le)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ee,w.value]]),C.value>0?(f(),k("div",Pe,[e(Z,{background:"",layout:"prev, pager, next",total:C.value,"current-page":p.value,"page-size":Y,onCurrentChange:s},null,8,["total","current-page"])])):pe("",!0)]),e(xe,{visible:x.value,"complaint-data":y.value,"onUpdate:visible":_,onConfirm:U},null,8,["visible","complaint-data"])])}}});const el=H(je,[["__scopeId","data-v-6816b05d"]]);export{el as default};
